// This script helps with the build process for SEO
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

// Get the directory name in ESM
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Update the sitemap.xml with the current date
const updateSitemap = () => {
  const sitemapPath = path.join(__dirname, "public", "sitemap.xml");
  if (fs.existsSync(sitemapPath)) {
    let sitemap = fs.readFileSync(sitemapPath, "utf8");
    const today = new Date().toISOString().split("T")[0];
    sitemap = sitemap.replace(
      /<lastmod>\d{4}-\d{2}-\d{2}<\/lastmod>/g,
      `<lastmod>${today}</lastmod>`
    );
    fs.writeFileSync(sitemapPath, sitemap);
    console.log("✅ Updated sitemap.xml with current date");
  }
};

// Create a simple script to verify the build
const verifyBuild = () => {
  const buildDir = path.join(__dirname, "build");
  if (fs.existsSync(buildDir)) {
    const files = fs.readdirSync(buildDir);
    console.log(
      "✅ Build directory contains:",
      files.length,
      "files/directories"
    );

    // Check for important SEO files
    const seoFiles = [
      "robots.txt",
      "sitemap.xml",
      ".htaccess",
      "manifest.json",
    ];
    seoFiles.forEach((file) => {
      if (fs.existsSync(path.join(buildDir, file))) {
        console.log(`✅ ${file} exists in build directory`);
      } else {
        console.log(`❌ ${file} is missing from build directory`);
      }
    });
  } else {
    console.log("❌ Build directory does not exist");
  }
};

// Run the functions
updateSitemap();
console.log("SEO preparation complete. You can now run the build command.");
console.log(
  'After building, run "node build-seo.js verify" to check the build.'
);

// Check if we should verify the build
if (process.argv[2] === "verify") {
  verifyBuild();
}
