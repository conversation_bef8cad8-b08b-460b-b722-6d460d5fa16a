// Dynamic route configuration that matches the actual React Router setup
const SUPPORTED_LANGUAGES = ["en", "et", "fi", "de", "sv"];
const PUBLIC_ROUTES = [
  "",
  "about",
  "services",
  "portfolio",
  "blog",
  "webstore",
  "contact",
];

// Generate all language-prefixed routes dynamically
const generateRoutes = () => {
  const routes = ["/"]; // Root redirect

  // Add all language-prefixed routes
  SUPPORTED_LANGUAGES.forEach((lang) => {
    PUBLIC_ROUTES.forEach((route) => {
      if (route === "") {
        routes.push(`/${lang}`);
      } else {
        routes.push(`/${lang}/${route}`);
      }
    });
  });

  // Add 404 page
  routes.push("/404");

  return routes;
};

module.exports = {
  // Output directory (same as in vite.config.js)
  destination: "build",
  // Include all important routes for SEO with language prefixes - dynamically generated
  include: generateRoutes(),
  // Wait for network requests to complete
  puppeteerArgs: [
    "--no-sandbox",
    "--disable-setuid-sandbox",
    "--disable-web-security",
    "--disable-features=IsolateOrigins,site-per-process",
    "--js-flags=--harmony",
  ],
  // Reduce concurrency to avoid issues
  concurrency: 1,
  // Increase timeout for slow machines
  puppeteerTimeout: 60000,
  // Skip minification to avoid issues
  minifyHtml: false,
  // Ignore specific resources to speed up the process
  skipThirdPartyRequests: false,
  // Wait until page is fully loaded
  waitFor: 1000,
  // Handle JavaScript errors
  onError: (error) => {
    console.error("Error during prerendering:", error);
    // Don't fail the build on errors
    return true;
  },
  // Use a more modern browser
  userAgent:
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
};
