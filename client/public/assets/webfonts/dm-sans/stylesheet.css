@font-face {
    font-family: 'DM Sans';
    src: local('DM Sans Black'), local('DMSans-Black'),
        url('DMSans-Black.woff2') format('woff2'),
        url('DMSans-Black.woff') format('woff');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'DM Sans';
    src: local('DM Sans Medium'), local('DMSans-Medium'),
        url('DMSans-Medium.woff2') format('woff2'),
        url('DMSans-Medium.woff') format('woff');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'DM Sans';
    src: local('DM Sans Medium Italic'), local('DMSans-MediumItalic'),
        url('DMSans-MediumItalic.woff2') format('woff2'),
        url('DMSans-MediumItalic.woff') format('woff');
    font-weight: 500;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'DM Sans';
    src: local('DM Sans Thin'), local('DMSans-Thin'),
        url('DMSans-Thin.woff2') format('woff2'),
        url('DMSans-Thin.woff') format('woff');
    font-weight: 100;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'DM Sans';
    src: local('DM Sans Thin Italic'), local('DMSans-ThinItalic'),
        url('DMSans-ThinItalic.woff2') format('woff2'),
        url('DMSans-ThinItalic.woff') format('woff');
    font-weight: 100;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'DM Sans';
    src: local('DM Sans Bold'), local('DMSans-Bold'),
        url('DMSans-Bold.woff2') format('woff2'),
        url('DMSans-Bold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'DM Sans';
    src: local('DM Sans Regular'), local('DMSans-Regular'),
        url('DMSans-Regular.woff2') format('woff2'),
        url('DMSans-Regular.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'DM Sans';
    src: local('DM Sans SemiBold'), local('DMSans-SemiBold'),
        url('DMSans-SemiBold.woff2') format('woff2'),
        url('DMSans-SemiBold.woff') format('woff');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'DM Sans';
    src: local('DM Sans Light'), local('DMSans-Light'),
        url('DMSans-Light.woff2') format('woff2'),
        url('DMSans-Light.woff') format('woff');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'DM Sans';
    src: local('DM Sans Black Italic'), local('DMSans-BlackItalic'),
        url('DMSans-BlackItalic.woff2') format('woff2'),
        url('DMSans-BlackItalic.woff') format('woff');
    font-weight: 900;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'DM Sans';
    src: local('DM Sans Light Italic'), local('DMSans-LightItalic'),
        url('DMSans-LightItalic.woff2') format('woff2'),
        url('DMSans-LightItalic.woff') format('woff');
    font-weight: 300;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'DM Sans';
    src: local('DM Sans ExtraLight'), local('DMSans-ExtraLight'),
        url('DMSans-ExtraLight.woff2') format('woff2'),
        url('DMSans-ExtraLight.woff') format('woff');
    font-weight: 200;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'DM Sans';
    src: local('DM Sans Italic'), local('DMSans-Italic'),
        url('DMSans-Italic.woff2') format('woff2'),
        url('DMSans-Italic.woff') format('woff');
    font-weight: normal;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'DM Sans';
    src: local('DM Sans ExtraBold'), local('DMSans-ExtraBold'),
        url('DMSans-ExtraBold.woff2') format('woff2'),
        url('DMSans-ExtraBold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

