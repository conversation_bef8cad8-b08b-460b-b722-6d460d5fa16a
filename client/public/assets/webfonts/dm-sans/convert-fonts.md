# DM Sans Font Conversion Instructions

## Required Files to Convert:
From your dm-sans/static folder, we need to convert these 3 files:

1. `DMSans-Regular.ttf` → WOFF2 + WOFF
2. `DMSans-Medium.ttf` → WOFF2 + WOFF  
3. `DMSans-Bold.ttf` → WOFF2 + WOFF

## Conversion Options:

### Option 1: Online Converter (Recommended)
1. Go to: https://cloudconvert.com/ttf-to-woff2
2. Upload each TTF file
3. Convert to both WOFF2 and WOFF formats
4. Download and place in this folder

### Option 2: Command Line (if you have fonttools)
```bash
# Install fonttools if needed
pip install fonttools[woff]

# Convert to WOFF2
fonttools ttLib.woff2 compress DMSans-Regular.ttf
fonttools ttLib.woff2 compress DMSans-Medium.ttf  
fonttools ttLib.woff2 compress DMSans-Bold.ttf

# Convert to WOFF
fonttools ttLib.woff compress DMSans-Regular.ttf
fonttools ttLib.woff compress DMSans-Medium.ttf
fonttools ttLib.woff compress DMSans-Bold.ttf
```

## Final File Structure Needed:
```
/public/assets/webfonts/dm-sans/
├── DMSans-Regular.woff2
├── DMSans-Regular.woff
├── DMSans-Medium.woff2
├── DMSans-Medium.woff
├── DMSans-Bold.woff2
└── DMSans-Bold.woff
```

Please convert the fonts and let me know when ready to proceed with the CSS implementation.
