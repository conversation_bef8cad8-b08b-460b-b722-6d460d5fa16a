/* CRITICAL CSS - Only essential styles for above-the-fold content */

/* Variables - Essential only */
:root {
  --font-global: "DM Sans", sans-serif;
  --font-alt: "DM Sans", sans-serif;
  --font-serif: Georgia, "Times New Roman", Times, serif;
  --full-wrapper-margin-x: 30px;
  --container-width: 1350px;
  --section-padding-y: 120px;
  --menu-bar-height: 85px;
  --menu-bar-height-scrolled: 65px;
  --color-dark-1: #010101;
  --color-dark-2: #171717;
  --color-dark-3: #272727;
  --color-dark-3a: #333;
  --color-dark-4: #555;
  --color-gray-1: #757575;
  --color-gray-2: #888;
  --color-gray-3: #999;
  --color-gray-light-1: #f1f1f1;
  --color-gray-light-2: #f7f7f7;
  --color-gray-light-3: #e5e5e5;
  --color-gray-light-4: #d5d5d5;
  --color-gray-light-5: #ccc;
  --color-gray-light-6: #bbb;
  --color-dark-mode-gray-1: rgba(255, 255, 255, 0.7);
  --color-dark-mode-gray-2: rgba(255, 255, 255, 0.1275);
  --color-primary-1: #6366f1;
  --color-primary-2: #4f46e5;
  --color-primary-3: #4338ca;
}

/* Essential Typography */
body {
  font-family: var(--font-global);
  font-size: 16px;
  line-height: 1.6;
  color: #333;
  background-color: #000;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-alt);
  font-weight: 500;
  line-height: 1.2;
  margin: 0 0 1rem 0;
}

.hs-title-3 {
  font-size: 4rem;
  font-weight: 500;
  line-height: 1.1;
}

.section-title-tiny {
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

/* Essential Layout */
.container {
  max-width: var(--container-width);
  margin: 0 auto;
  padding: 0 var(--full-wrapper-margin-x);
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
}

.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
}
.col-6 {
  flex: 0 0 50%;
  max-width: 50%;
}
.col-4 {
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
}
.col-3 {
  flex: 0 0 25%;
  max-width: 25%;
}

/* Essential Utilities */
.d-flex {
  display: flex;
}
.align-items-center {
  align-items: center;
}
.justify-content-center {
  justify-content: center;
}
.justify-content-end {
  justify-content: end;
}
.text-center {
  text-align: center;
}
.mb-0 {
  margin-bottom: 0;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-3 {
  margin-bottom: 1rem;
}
.mb-4 {
  margin-bottom: 1.5rem;
}
.mb-5 {
  margin-bottom: 3rem;
}
.mt-3 {
  margin-top: 1rem;
}
.pt-100 {
  padding-top: 100px;
}
.pb-100 {
  padding-bottom: 100px;
}

/* Essential Theme Classes */
.theme-elegant {
}
.dark-mode {
}
.bg-dark-1 {
  background-color: var(--color-dark-1);
}
.bg-dark-2 {
  background-color: var(--color-dark-2);
}
.bg-dark-alpha-30 {
  background-color: rgba(1, 1, 1, 0.3);
}
.light-content {
  color: #fff;
}

/* Essential Navigation */
.main-nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  height: var(--menu-bar-height);
  transition: all 0.3s ease;
}

.main-nav.transparent {
  background-color: transparent;
}

.main-nav.stick-fixed {
  background-color: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
}

/* Essential Buttons */
.btn {
  display: inline-block;
  padding: 12px 24px;
  border: none;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-mod {
  background-color: var(--color-primary-1);
  color: #fff;
}

.btn-border-w {
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: #fff;
  background-color: transparent;
}

.btn-small {
  padding: 8px 16px;
  font-size: 0.875rem;
}

.btn-circle {
  border-radius: 50px;
}

/* Essential Animations - Critical for LCP */
.wow,
.wow-menubar,
.wow-p {
  opacity: 0.001;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

html:not(.mobile) .wow,
html:not(.mobile) .wow-menubar,
html:not(.mobile) .wow-p {
  /*will-change: opacity, transform;*/
}

.appear-animate .wow.scaleOutIn {
  opacity: 1;
  transform: scale(1.2);
}

.appear-animate .wow.animated,
.appear-animate .wow.scaleOutIn.animated,
.appear-animate .wow-p.amimated,
.appear-animate .wow-menubar.animated,
.mobile .appear-animate .wow,
.mobile .appear-animate .wow-menubar,
.mobile .appear-animate .wow-p {
  opacity: 1;
  transform: scale(1);
}

.animated {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-duration: var(--animate-duration);
  animation-duration: var(--animate-duration);
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fadeIn {
  animation-name: fadeIn;
}

@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translate3d(0, -37px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

.fadeInDown {
  animation-name: fadeInDown;
}

.fadeInDownShort {
  animation: fadeInDown 0.6s ease-out;
}

.fadeInUpShort {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translate3d(0, 37px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Essential Margin Utilities - Critical for LCP */
.mb-0 {
  margin-bottom: 0 !important;
}

.mb-10 {
  margin-bottom: 10px !important;
}

.mb-20 {
  margin-bottom: 20px !important;
}

.mb-30 {
  margin-bottom: 30px !important;
}

.mb-40 {
  margin-bottom: 40px !important;
}

.mb-50 {
  margin-bottom: 50px !important;
}

.mb-60 {
  margin-bottom: 60px !important;
}

.mb-70 {
  margin-bottom: 70px !important;
}

.mb-80 {
  margin-bottom: 80px !important;
}

.mb-90 {
  margin-bottom: 90px !important;
}

.mb-100 {
  margin-bottom: 100px !important;
}

/* Responsive margin utilities for mobile */
@media (max-width: 767px) {
  .mb-sm-10 {
    margin-bottom: 10px !important;
  }

  .mb-sm-20 {
    margin-bottom: 20px !important;
  }

  .mb-sm-30 {
    margin-bottom: 30px !important;
  }

  .mb-sm-40 {
    margin-bottom: 40px !important;
  }

  .mb-sm-50 {
    margin-bottom: 50px !important;
  }
}

/* Essential Section Styles - Critical for LCP */
.page-section,
.small-section,
.bg-image {
  width: 100%;
  display: block;
  position: relative;
  background-attachment: fixed;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  padding-top: var(--section-padding-y);
  padding-bottom: var(--section-padding-y);
}

.small-section {
  padding: 100px 0;
}

/* Essential Container Styles */
.full-wrapper {
  margin: 0 var(--full-wrapper-margin-x);
}

.container {
  max-width: var(--container-width);
  padding: 0 30px;
}

/* Essential Text Center */
.text-center {
  text-align: center !important;
}

/* Essential Display Utilities */
.d-block {
  display: block !important;
}

.d-inline-block {
  display: inline-block !important;
}

/* Essential Position Utilities */
.position-relative {
  position: relative !important;
}

/* Essential Width Utilities */
.w-100 {
  width: 100% !important;
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Essential Home Section */
.home-section {
  min-height: 100vh;
  position: relative;
  display: flex;
  align-items: center;
}

.home-content {
  width: 100%;
}

.min-height-100vh {
  min-height: 100vh;
}

/* Essential Parallax */
.parallax-5 {
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
}

/* Essential Mobile Responsive */
@media (max-width: 768px) {
  .hs-title-3 {
    font-size: 2.5rem;
  }
  .container {
    padding: 0 20px;
  }
  .pt-sm-120 {
    padding-top: 120px;
  }
  .pb-sm-120 {
    padding-bottom: 120px;
  }
  .mb-sm-30 {
    margin-bottom: 30px;
  }
  .mb-sm-80 {
    margin-bottom: 80px;
  }
}

@media (max-width: 480px) {
  .mb-xs-140 {
    margin-bottom: 140px;
  }
}

/* Essential Link Animations */
.link-hover-anim {
  position: relative;
  text-decoration: none;
  transition: all 0.3s ease;
}

.link-circle-1 {
  display: inline-flex;
  align-items: center;
}

.link-strong {
  font-weight: 500;
}

/* Essential Page Structure */
.page {
  min-height: 100vh;
}

#main {
  position: relative;
}

.footer {
  position: relative;
}

.z-index-1 {
  z-index: 1;
}

.position-relative {
  position: relative;
}

/* Essential Visibility */
.visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}
