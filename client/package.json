{"name": "resonence-react", "private": true, "version": "1.2.0", "type": "module", "scripts": {"dev": "vite", "prebuild": "node build-seo.js", "build": "vite build", "postbuild": "echo 'Build completed successfully'", "verify": "node build-seo.js verify", "generate-og": "node generate-og-images-new.js", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "seo-build": "npm run generate-og && npm run build"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@iconify/react": "^6.0.0", "@popperjs/core": "2.11.8", "@tinymce/tinymce-react": "^6.2.1", "@tiptap/extension-code": "^2.22.3", "@tiptap/extension-code-block-lowlight": "^2.22.3", "@tiptap/react": "^2.22.3", "@tiptap/starter-kit": "^2.22.3", "axios": "^1.8.4", "bootstrap": "^5.1.3", "chart.js": "^4.5.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "imagesloaded": "^5.0.0", "isotope-layout": "^3.0.6", "jarallax": "^2.2.1", "lowlight": "^3.3.0", "photoswipe": "^5.4.4", "prop-types": "^15.8.1", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-helmet-async": "^2.0.5", "react-i18next": "^15.5.3", "react-modal-video": "^2.0.2", "react-photoswipe-gallery": "^3.0.2", "react-router-dom": "^6.25.1", "rellax": "^1.12.1", "shiki": "^3.7.0", "swiper": "^11.1.8", "terser": "^5.39.0", "tinymce": "^7.9.1", "tippy.js": "^6.3.7", "typewriter-effect": "^2.21.0", "wow.js": "^1.2.2", "wowjs": "^1.1.3", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/js": "^9.20.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^9.20.1", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.7", "globals": "^15.15.0", "sharp": "^0.34.0", "vite": "^5.3.4"}, "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0"}