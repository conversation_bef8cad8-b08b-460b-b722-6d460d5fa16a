/* GDPR Modal Styles - Banner styles are inlined in HTML for LCP performance */

/* Modal specific styles */
.gdpr-modal-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #ffffff !important;
}

.gdpr-category-title {
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff !important;
}

.gdpr-category-desc {
  font-size: 0.875rem;
  line-height: 1.4;
  color: #e0e0e0;
}

.gdpr-required-badge {
  background: rgba(6, 182, 212, 0.2);
  color: #06b6d4;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: 1px solid rgba(6, 182, 212, 0.3);
  margin-left: 20px;
}

.gdpr-category-header {
  margin-bottom: 15px !important;
}

.gdpr-switch {
  margin-left: 20px;
}

.gdpr-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.gdpr-btn-accept {
  background: #06b6d4;
  color: #ffffff;
}

.gdpr-btn-accept:hover {
  background: #0891b2;
  transform: translateY(-2px);
}

.gdpr-btn-reject {
  background: #6b7280;
  color: #ffffff;
}

.gdpr-btn-reject:hover {
  background: #4b5563;
  transform: translateY(-2px);
}

.gdpr-btn-customize {
  background: transparent;
  color: #06b6d4;
  border: 2px solid #06b6d4;
}

.gdpr-btn-customize:hover {
  background: #06b6d4;
  color: #ffffff;
  transform: translateY(-2px);
}

/* Modal Styles */
.gdpr-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  padding: 20px;
}

.gdpr-modal-content {
  background: rgba(40, 40, 40, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.gdpr-modal-inner {
  padding: 30px;
}

.gdpr-modal-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #ffffff;
}

.gdpr-close {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  padding: 8px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.gdpr-close:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  transform: scale(1.1);
}

.gdpr-modal-body {
  padding: 24px;
}

.gdpr-category {
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #333;
}

.gdpr-category:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.gdpr-category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.gdpr-category-header h4 {
  margin: 0;
  color: #ffffff;
  font-size: 1.1rem;
}

.badge.bg-primary {
  background-color: #06b6d4 !important;
  color: #ffffff;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.gdpr-category p {
  color: #e0e0e0;
  line-height: 1.6;
  margin: 0;
}

/* Toggle Switch */
.gdpr-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.gdpr-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.gdpr-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #333;
  transition: 0.3s;
  border-radius: 24px;
}

.gdpr-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .gdpr-slider {
  background-color: #06b6d4;
}

input:checked + .gdpr-slider:before {
  transform: translateX(26px);
}

.gdpr-modal-footer {
  padding: 24px;
  border-top: 1px solid #333;
  display: flex;
  gap: 15px;
  justify-content: flex-end;
}

.gdpr-btn-primary {
  background: #06b6d4;
  color: #ffffff;
}

.gdpr-btn-primary:hover {
  background: #0891b2;
}

.gdpr-btn-secondary {
  background: transparent;
  color: #888;
  border: 1px solid #333;
}

.gdpr-btn-secondary:hover {
  color: #ffffff;
  border-color: #555;
}

/* Fix button text color on hover for filled buttons */
.gdpr-buttons .btn-w:hover,
.gdpr-modal-footer .btn-w:hover {
  color: #6b7280 !important; /* gray-500 */
}

.gdpr-buttons .btn-w:hover .btn-animate-y-1,
.gdpr-buttons .btn-w:hover .btn-animate-y-2,
.gdpr-modal-footer .btn-w:hover .btn-animate-y-1,
.gdpr-modal-footer .btn-w:hover .btn-animate-y-2 {
  color: #6b7280 !important; /* gray-500 */
}

/* Modal Responsive Design */
@media (max-width: 768px) {
  .gdpr-modal {
    padding: 10px;
  }

  .gdpr-modal-content {
    max-height: 90vh;
  }

  .gdpr-modal-inner {
    padding: 20px;
  }

  .gdpr-category-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .gdpr-switch {
    margin-left: 0;
  }

  .gdpr-modal-footer .d-flex {
    flex-direction: column;
    gap: 10px;
  }
}
