/* Benefits Cards Hover Effects */

.benefits-card {
  transition: all 0.3s ease;
  filter: grayscale(100%);
  cursor: pointer;
}

.benefits-card:hover {
  filter: grayscale(0%);
}

/* Icon flip animation - exactly like link-hover-anim */
.icon-hover-anim {
  position: relative;
  display: inline-block;
  overflow: hidden;
}

.icon-strong {
  position: relative;
  display: inline-block;
  overflow: hidden;
  transition: transform 0.5s var(--ease-elastic-1, ease),
    opacity 0.5s var(--ease-elastic-1, ease),
    color 0.2s var(--ease-default, ease);
}

.icon-strong-hovered {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  transform: translateY(150%) translateZ(0.001px) skewY(10deg);
}

.icon-strong-unhovered {
  display: block;
}

.benefits-card:hover .icon-strong-unhovered {
  opacity: 0;
  transform: translateY(-150%) translateZ(0.001px) skewY(-10deg);
}

.benefits-card:hover .icon-strong-hovered {
  opacity: 1;
  transform: translateY(0) translateZ(0.001px) skewY(0);
}

/* Center icons within the gradient circle */
.benefits-card .alt-features-icon.icon-hover-anim {
  display: flex;
  align-items: center;
  justify-content: center;
}

.benefits-card .icon-strong {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.benefits-card .icon-strong svg {
  display: block;
}
