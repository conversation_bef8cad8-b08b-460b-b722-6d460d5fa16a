/* TipTap Editor Styles */
.tiptap-wrapper {
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  background: #fff;
  overflow: hidden;
}

.tiptap-toolbar {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 12px;
  background: #f8f9fa;
  border-bottom: 1px solid #e1e5e9;
}

.toolbar-group {
  display: flex;
  gap: 4px;
  align-items: center;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid transparent;
  border-radius: 4px;
  background: transparent;
  color: #6c757d;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toolbar-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.toolbar-btn.active {
  background: #4567e7;
  color: white;
  border-color: #4567e7;
}

.toolbar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-btn:disabled:hover {
  background: transparent;
  color: #6c757d;
}

.tiptap-content {
  min-height: 300px;
}

.tiptap-editor {
  padding: 20px;
  outline: none;
  min-height: 300px;
  line-height: 1.6;
  font-size: 16px;
  color: #333;
}

.tiptap-editor p {
  margin: 0 0 16px 0;
}

.tiptap-editor p:last-child {
  margin-bottom: 0;
}

.tiptap-editor h1,
.tiptap-editor h2,
.tiptap-editor h3,
.tiptap-editor h4,
.tiptap-editor h5,
.tiptap-editor h6 {
  margin: 24px 0 16px 0;
  font-weight: 600;
  line-height: 1.3;
}

.tiptap-editor h1:first-child,
.tiptap-editor h2:first-child,
.tiptap-editor h3:first-child {
  margin-top: 0;
}

.tiptap-editor h1 {
  font-size: 2rem;
}

.tiptap-editor h2 {
  font-size: 1.5rem;
}

.tiptap-editor h3 {
  font-size: 1.25rem;
}

.tiptap-editor ul,
.tiptap-editor ol {
  margin: 16px 0;
  padding-left: 24px;
}

.tiptap-editor li {
  margin: 4px 0;
}

.tiptap-editor blockquote {
  margin: 16px 0;
  padding: 16px 20px;
  border-left: 4px solid #4567e7;
  background: #f8f9fa;
  font-style: italic;
}

.tiptap-editor hr {
  margin: 24px 0;
  border: none;
  border-top: 2px solid #e1e5e9;
}

/* Inline Code Styling */
.tiptap-editor .inline-code {
  background: #000;
  color: #00ff00;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.9em;
  font-weight: 500;
}

/* Code Block Styling */
.tiptap-editor .code-block {
  background: #000 !important;
  color: #fff;
  padding: 20px;
  border-radius: 8px;
  margin: 16px 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.5;
  overflow-x: auto;
  position: relative;
}

.tiptap-editor .code-block pre {
  margin: 0;
  padding: 0;
  background: transparent;
  color: inherit;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

.tiptap-editor .code-block code {
  background: transparent;
  color: inherit;
  padding: 0;
  border-radius: 0;
  font-family: inherit;
  font-size: inherit;
}

/* Syntax Highlighting Colors for Dark Theme */
.tiptap-editor .code-block .hljs-keyword {
  color: #ff79c6;
}

.tiptap-editor .code-block .hljs-string {
  color: #f1fa8c;
}

.tiptap-editor .code-block .hljs-number {
  color: #bd93f9;
}

.tiptap-editor .code-block .hljs-comment {
  color: #6272a4;
  font-style: italic;
}

.tiptap-editor .code-block .hljs-function {
  color: #50fa7b;
}

.tiptap-editor .code-block .hljs-variable {
  color: #8be9fd;
}

.tiptap-editor .code-block .hljs-title {
  color: #50fa7b;
}

.tiptap-editor .code-block .hljs-attr {
  color: #50fa7b;
}

.tiptap-editor .code-block .hljs-tag {
  color: #ff79c6;
}

.tiptap-editor .code-block .hljs-name {
  color: #ff79c6;
}

.tiptap-editor .code-block .hljs-selector-tag {
  color: #ff79c6;
}

.tiptap-editor .code-block .hljs-selector-class {
  color: #50fa7b;
}

.tiptap-editor .code-block .hljs-selector-id {
  color: #50fa7b;
}

.tiptap-editor .code-block .hljs-built_in {
  color: #8be9fd;
}

.tiptap-editor .code-block .hljs-type {
  color: #8be9fd;
}

.tiptap-editor .code-block .hljs-literal {
  color: #bd93f9;
}

.tiptap-editor .code-block .hljs-meta {
  color: #ff79c6;
}

.tiptap-editor .code-block .hljs-doctag {
  color: #ff79c6;
}

/* Placeholder styling */
.tiptap-editor p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

/* Focus styles */
.tiptap-wrapper:focus-within {
  border-color: #4567e7;
  box-shadow: 0 0 0 3px rgba(69, 103, 231, 0.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .tiptap-toolbar {
    padding: 8px;
    gap: 4px;
  }
  
  .toolbar-btn {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
  
  .tiptap-editor {
    padding: 16px;
    font-size: 14px;
  }
  
  .tiptap-content {
    min-height: 250px;
  }
}
