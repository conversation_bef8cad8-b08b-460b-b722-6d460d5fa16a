import { useEffect } from "react";
import { useLocation } from "react-router-dom";

/**
 * Component to load admin-only CSS when on admin pages
 * This prevents admin CSS from bloating the main bundle
 */
export default function AdminCSS() {
  const location = useLocation();
  const isAdminPage = location.pathname.startsWith('/admin');

  useEffect(() => {
    if (isAdminPage) {
      // Load admin-specific CSS libraries
      const loadAdminCSS = () => {
        const adminCSSLibraries = [
          "https://cdn.jsdelivr.net/npm/tippy.js@6.3.7/dist/tippy.css"
        ];

        adminCSSLibraries.forEach(href => {
          // Check if CSS is already loaded
          if (!document.querySelector(`link[href="${href}"]`)) {
            const link = document.createElement("link");
            link.rel = "stylesheet";
            link.href = href;
            document.head.appendChild(link);
          }
        });

        // Load admin.css if it exists
        const adminCSSPath = "/src/styles/admin.css";
        if (!document.querySelector(`link[href="${adminCSSPath}"]`)) {
          const link = document.createElement("link");
          link.rel = "stylesheet";
          link.href = adminCSSPath;
          document.head.appendChild(link);
        }
      };

      loadAdminCSS();
    }
  }, [isAdminPage]);

  return null; // This component doesn't render anything
}
