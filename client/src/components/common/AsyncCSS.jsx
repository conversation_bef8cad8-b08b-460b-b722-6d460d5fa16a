import { useEffect } from "react";

/**
 * Component to load non-critical CSS asynchronously after page load
 * This improves initial page load performance while maintaining functionality
 */
export default function AsyncCSS() {
  useEffect(() => {
    // Load non-critical CSS libraries asynchronously
    const loadAsyncCSS = () => {
      const cssLibraries = [
        "https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css",
        "https://cdn.jsdelivr.net/npm/react-modal-video@1.2.1/css/modal-video.min.css",
        "https://cdn.jsdelivr.net/npm/photoswipe@5.4.2/dist/photoswipe.css"
      ];

      cssLibraries.forEach(href => {
        // Check if CSS is already loaded
        if (!document.querySelector(`link[href="${href}"]`)) {
          const link = document.createElement("link");
          link.rel = "stylesheet";
          link.href = href;
          link.media = "print"; // Load as print media first (non-blocking)
          link.onload = () => {
            link.media = "all"; // Switch to all media once loaded
          };
          document.head.appendChild(link);
        }
      });
    };

    // Load after a short delay to ensure critical content renders first
    const timer = setTimeout(loadAsyncCSS, 100);

    return () => clearTimeout(timer);
  }, []);

  return null; // This component doesn't render anything
}
