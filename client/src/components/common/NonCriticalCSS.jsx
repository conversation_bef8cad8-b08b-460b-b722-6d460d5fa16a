import { useEffect } from 'react';

/**
 * Component to load non-critical CSS asynchronously after critical content renders
 * This improves PageSpeed scores by preventing render-blocking CSS
 */
export default function NonCriticalCSS() {
  useEffect(() => {
    // Load non-critical CSS after component mounts (after critical content renders)
    const loadNonCriticalCSS = () => {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = '/assets/css/non-critical.css';
      link.media = 'print'; // Load as print media first (non-blocking)
      link.onload = () => {
        link.media = 'all'; // Switch to all media once loaded
      };
      document.head.appendChild(link);
    };

    // Load after a short delay to ensure critical content renders first
    const timer = setTimeout(loadNonCriticalCSS, 100);

    return () => clearTimeout(timer);
  }, []);

  return null; // This component doesn't render anything
}
