import { useEffect } from "react";

/**
 * Component to load non-critical CSS asynchronously after critical content renders
 * This improves PageSpeed scores by preventing render-blocking CSS
 */
export default function NonCriticalCSS() {
  useEffect(() => {
    // Load main CSS bundle and additional libraries after critical content renders
    const loadNonCriticalCSS = () => {
      // Load main CSS bundle
      const mainLink = document.createElement("link");
      mainLink.rel = "stylesheet";
      mainLink.href = "/assets/css/style.css"; // Main CSS bundle
      mainLink.media = "print"; // Load as print media first (non-blocking)
      mainLink.onload = () => {
        mainLink.media = "all"; // Switch to all media once loaded
      };
      document.head.appendChild(mainLink);

      // Load Swiper CSS for components that need it
      const swiperLink = document.createElement("link");
      swiperLink.rel = "stylesheet";
      swiperLink.href = "https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css";
      swiperLink.media = "print";
      swiperLink.onload = () => {
        swiperLink.media = "all";
      };
      document.head.appendChild(swiperLink);
    };

    // Load after a short delay to ensure critical content renders first
    const timer = setTimeout(loadNonCriticalCSS, 50);

    return () => clearTimeout(timer);
  }, []);

  return null; // This component doesn't render anything
}
