import React from "react";
import { useTranslation } from "react-i18next";
import { languages } from "@/i18n";

export default function I18nDebug() {
  const { i18n, t, ready } = useTranslation();

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div
      style={{
        position: "fixed",
        top: "10px",
        right: "10px",
        background: "rgba(0,0,0,0.8)",
        color: "white",
        padding: "10px",
        borderRadius: "5px",
        fontSize: "12px",
        zIndex: 10000,
        maxWidth: "300px",
      }}
    >
      <h4>i18n Debug</h4>
      <p><strong>Ready:</strong> {ready ? "✅" : "❌"}</p>
      <p><strong>Current Language:</strong> {i18n.language}</p>
      <p><strong>Supported Languages:</strong> {Object.keys(languages).join(", ")}</p>
      <p><strong>Test Translation:</strong> {t('nav.home', 'Home')}</p>
      <p><strong>Blog Title:</strong> {t('blog.title', 'Latest Insights')}</p>
      
      <div style={{ marginTop: "10px" }}>
        <strong>Quick Language Test:</strong>
        <div style={{ display: "flex", gap: "5px", marginTop: "5px" }}>
          {Object.keys(languages).map((lang) => (
            <button
              key={lang}
              onClick={() => i18n.changeLanguage(lang)}
              style={{
                padding: "2px 6px",
                fontSize: "10px",
                background: i18n.language === lang ? "#007bff" : "#333",
                color: "white",
                border: "none",
                borderRadius: "3px",
                cursor: "pointer",
              }}
            >
              {lang.toUpperCase()}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
