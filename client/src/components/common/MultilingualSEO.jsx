import React from "react";
import PropTypes from "prop-types";
import { Helmet } from "react-helmet-async";
import { useTranslation } from "react-i18next";

/**
 * Multilingual SEO component with proper hreflang tags and language-specific meta data
 *
 * @param {Object} props - Component props
 * @param {string} props.title - Page title (will be translated if key provided)
 * @param {string} props.description - Page description (will be translated if key provided)
 * @param {string} props.slug - Page slug for generating language URLs
 * @param {string} props.type - Page type (website, article, etc.)
 * @param {Object} props.schema - JSON-LD structured data
 * @param {string[]} props.keywords - Keywords for SEO
 * @param {string} props.image - OG image URL
 * @param {Object} props.alternateUrls - Custom alternate URLs for languages
 */
const MultilingualSEO = ({
  title,
  description,
  slug = "",
  type = "website",
  schema = null,
  keywords = [],
  image = "https://devskills.ee/og-image.jpg",
  alternateUrls = null,
}) => {
  const { currentLanguage } = useTranslation();

  // Generate language-specific URLs
  const baseUrl = "https://devskills.ee";
  const generateLanguageUrls = () => {
    if (alternateUrls) {
      return alternateUrls;
    }

    const urls = {
      en: `${baseUrl}${slug ? `/${slug}` : ""}`,
      et: `${baseUrl}/et${slug ? `/${slug}` : ""}`,
      de: `${baseUrl}/de${slug ? `/${slug}` : ""}`,
    };

    return urls;
  };

  const languageUrls = generateLanguageUrls();
  const currentUrl = languageUrls[currentLanguage];

  // Language-specific meta data
  const getLanguageSpecificData = () => {
    const localeMap = {
      en: "en_US",
      et: "et_EE",
      de: "de_DE",
    };

    const languageMap = {
      en: "English",
      et: "Estonian",
      de: "German",
    };

    return {
      locale: localeMap[currentLanguage] || "en_US",
      language: languageMap[currentLanguage] || "English",
    };
  };

  const { locale, language } = getLanguageSpecificData();

  // Format the title with company name
  const formattedTitle = `${title} | DevSkills`;

  return (
    <Helmet>
      {/* Basic meta tags */}
      <title>{formattedTitle}</title>
      <meta name="description" content={description} />
      <link rel="canonical" href={currentUrl} />
      <meta name="keywords" content={keywords.join(", ")} />
      <meta name="language" content={language} />

      {/* Hreflang tags for multilingual SEO */}
      {Object.entries(languageUrls).map(([lang, url]) => (
        <link key={lang} rel="alternate" hrefLang={lang} href={url} />
      ))}

      {/* x-default for international targeting */}
      <link rel="alternate" hrefLang="x-default" href={languageUrls.en} />

      {/* Open Graph meta tags */}
      <meta property="og:title" content={formattedTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={type} />
      <meta property="og:url" content={currentUrl} />
      <meta property="og:image" content={image} />
      <meta property="og:image:alt" content={title} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:site_name" content="DevSkills" />
      <meta property="og:locale" content={locale} />

      {/* Alternate locales for Facebook */}
      {Object.keys(languageUrls)
        .filter((lang) => lang !== currentLanguage)
        .map((lang) => {
          const altLocale = {
            en: "en_US",
            et: "et_EE",
            de: "de_DE",
          }[lang];
          return (
            <meta
              key={lang}
              property="og:locale:alternate"
              content={altLocale}
            />
          );
        })}

      {/* Twitter Card meta tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content="@DevSkillsEE" />
      <meta name="twitter:creator" content="@DevSkillsEE" />
      <meta name="twitter:title" content={formattedTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
      <meta name="twitter:image:alt" content={title} />

      {/* Additional SEO meta tags */}
      <meta name="robots" content="index, follow, max-image-preview:large" />
      <meta name="googlebot" content="index, follow" />
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
      <meta httpEquiv="Content-Language" content={currentLanguage} />

      {/* Mobile and theme meta tags */}
      <meta
        name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=5.0"
      />
      <meta name="theme-color" content="#06B6D4" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta
        name="apple-mobile-web-app-status-bar-style"
        content="black-translucent"
      />

      {/* JSON-LD structured data */}
      {schema && (
        <script type="application/ld+json">
          {JSON.stringify({
            ...schema,
            "@context": "https://schema.org",
            inLanguage: currentLanguage,
            url: currentUrl,
          })}
        </script>
      )}
    </Helmet>
  );
};

MultilingualSEO.propTypes = {
  title: PropTypes.string.isRequired,
  description: PropTypes.string.isRequired,
  slug: PropTypes.string,
  type: PropTypes.string,
  schema: PropTypes.object,
  keywords: PropTypes.arrayOf(PropTypes.string),
  image: PropTypes.string,
  alternateUrls: PropTypes.object,
};

export default MultilingualSEO;
