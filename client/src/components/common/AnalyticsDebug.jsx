import React, { useState } from 'react';
import {
  trackButtonClick,
  trackNavigation,
  trackFileDownload,
  trackExternalLink,
  trackCustomEvent,
  trackEngagementMilestone,
  debugAnalytics
} from '@/utils/analytics';

const AnalyticsDebug = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [testResults, setTestResults] = useState([]);

  const addTestResult = (test, status) => {
    const timestamp = new Date().toLocaleTimeString();
    setTestResults(prev => [...prev, { test, status, timestamp }]);
  };

  const runTest = (testName, testFunction) => {
    try {
      testFunction();
      addTestResult(testName, '✅ Sent');
    } catch (error) {
      addTestResult(testName, `❌ Error: ${error.message}`);
    }
  };

  const testButtonClick = () => {
    runTest('Button Click', () => {
      trackButtonClick('Test Button', 'Analytics Debug Panel', {
        test_mode: true,
        button_type: 'debug'
      });
    });
  };

  const testNavigation = () => {
    runTest('Navigation Click', () => {
      trackNavigation('Test Menu Item', 'Debug Panel');
    });
  };

  const testFileDownload = () => {
    runTest('File Download', () => {
      trackFileDownload('test-file.pdf', 'PDF', 'Debug Panel');
    });
  };

  const testExternalLink = () => {
    runTest('External Link', () => {
      trackExternalLink('https://example.com', 'Test External Link', 'Debug Panel');
    });
  };

  const testCustomEvent = () => {
    runTest('Custom Event', () => {
      trackCustomEvent('test_custom_event', 'Debug', 'Custom Event Test', 1, {
        test_mode: true,
        debug_panel: true
      });
    });
  };

  const testEngagementMilestone = () => {
    runTest('Engagement Milestone', () => {
      trackEngagementMilestone('Debug Test Milestone', window.location.pathname, {
        milestone_type: 'debug',
        test_mode: true
      });
    });
  };

  const testScrollDepth = () => {
    runTest('Scroll Depth', () => {
      // Simulate scroll depth tracking
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'scroll', {
          event_category: 'User Engagement',
          event_label: '100% scrolled (debug test)',
          scroll_percentage: 100,
          page_location: window.location.href,
          test_mode: true,
          send_to: 'G-8NEGL4LL8Q'
        });
      }
    });
  };

  const testTimeOnPage = () => {
    runTest('Time on Page', () => {
      // Simulate time on page tracking
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'timing_complete', {
          name: 'page_view_time',
          value: 30, // 30 seconds
          event_category: 'User Engagement',
          event_label: window.location.pathname,
          test_mode: true,
          send_to: 'G-8NEGL4LL8Q'
        });
      }
    });
  };

  const runAllTests = () => {
    setTestResults([]);
    setTimeout(() => testButtonClick(), 100);
    setTimeout(() => testNavigation(), 200);
    setTimeout(() => testFileDownload(), 300);
    setTimeout(() => testExternalLink(), 400);
    setTimeout(() => testCustomEvent(), 500);
    setTimeout(() => testEngagementMilestone(), 600);
    setTimeout(() => testScrollDepth(), 700);
    setTimeout(() => testTimeOnPage(), 800);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const checkAnalyticsStatus = () => {
    debugAnalytics();
    const status = {
      gtag: typeof window !== 'undefined' && typeof window.gtag !== 'undefined',
      dataLayer: typeof window !== 'undefined' && Array.isArray(window.dataLayer),
      consent: localStorage.getItem('gdpr-consent')
    };
    
    addTestResult('Analytics Status Check', 
      `gtag: ${status.gtag ? '✅' : '❌'}, dataLayer: ${status.dataLayer ? '✅' : '❌'}, consent: ${status.consent ? '✅' : '❌'}`
    );
  };

  if (!isVisible) {
    return (
      <div style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        zIndex: 10001,
        backgroundColor: '#06B6D4',
        color: 'white',
        padding: '10px 15px',
        borderRadius: '25px',
        cursor: 'pointer',
        fontSize: '14px',
        fontWeight: 'bold',
        boxShadow: '0 4px 12px rgba(0,0,0,0.3)'
      }} onClick={() => setIsVisible(true)}>
        📊 Analytics Debug
      </div>
    );
  }

  return (
    <div style={{
      position: 'fixed',
      bottom: '20px',
      right: '20px',
      width: '400px',
      maxHeight: '600px',
      backgroundColor: '#1a1a1a',
      color: 'white',
      borderRadius: '12px',
      padding: '20px',
      zIndex: 10001,
      boxShadow: '0 8px 32px rgba(0,0,0,0.5)',
      overflow: 'hidden',
      display: 'flex',
      flexDirection: 'column'
    }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '15px' }}>
        <h3 style={{ margin: 0, fontSize: '18px' }}>📊 Analytics Debug</h3>
        <button 
          onClick={() => setIsVisible(false)}
          style={{
            background: 'none',
            border: 'none',
            color: '#888',
            fontSize: '20px',
            cursor: 'pointer',
            padding: '0'
          }}
        >
          ×
        </button>
      </div>

      <div style={{ marginBottom: '15px' }}>
        <button 
          onClick={checkAnalyticsStatus}
          style={{
            backgroundColor: '#06B6D4',
            color: 'white',
            border: 'none',
            padding: '8px 12px',
            borderRadius: '6px',
            cursor: 'pointer',
            marginRight: '10px',
            fontSize: '12px'
          }}
        >
          Check Status
        </button>
        <button 
          onClick={runAllTests}
          style={{
            backgroundColor: '#10B981',
            color: 'white',
            border: 'none',
            padding: '8px 12px',
            borderRadius: '6px',
            cursor: 'pointer',
            marginRight: '10px',
            fontSize: '12px'
          }}
        >
          Run All Tests
        </button>
        <button 
          onClick={clearResults}
          style={{
            backgroundColor: '#6B7280',
            color: 'white',
            border: 'none',
            padding: '8px 12px',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '12px'
          }}
        >
          Clear
        </button>
      </div>

      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '8px', marginBottom: '15px' }}>
        <button onClick={testButtonClick} style={buttonStyle}>Button Click</button>
        <button onClick={testNavigation} style={buttonStyle}>Navigation</button>
        <button onClick={testFileDownload} style={buttonStyle}>File Download</button>
        <button onClick={testExternalLink} style={buttonStyle}>External Link</button>
        <button onClick={testCustomEvent} style={buttonStyle}>Custom Event</button>
        <button onClick={testEngagementMilestone} style={buttonStyle}>Engagement</button>
        <button onClick={testScrollDepth} style={buttonStyle}>Scroll Depth</button>
        <button onClick={testTimeOnPage} style={buttonStyle}>Time on Page</button>
      </div>

      <div style={{
        backgroundColor: '#2a2a2a',
        borderRadius: '6px',
        padding: '10px',
        maxHeight: '300px',
        overflowY: 'auto',
        fontSize: '12px'
      }}>
        <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>Test Results:</div>
        {testResults.length === 0 ? (
          <div style={{ color: '#888' }}>No tests run yet</div>
        ) : (
          testResults.map((result, index) => (
            <div key={index} style={{ marginBottom: '4px', padding: '4px', backgroundColor: '#333', borderRadius: '4px' }}>
              <div style={{ fontWeight: 'bold' }}>{result.test}</div>
              <div style={{ color: result.status.includes('✅') ? '#10B981' : '#EF4444' }}>
                {result.status} - {result.timestamp}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

const buttonStyle = {
  backgroundColor: '#374151',
  color: 'white',
  border: '1px solid #4B5563',
  padding: '6px 8px',
  borderRadius: '4px',
  cursor: 'pointer',
  fontSize: '11px',
  transition: 'background-color 0.2s'
};

export default AnalyticsDebug;
