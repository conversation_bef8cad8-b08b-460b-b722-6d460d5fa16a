import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

// Supported languages
const SUPPORTED_LANGUAGES = ['en', 'et', 'fi', 'de', 'sv'];

// Get current language from URL
const getCurrentLanguage = (pathname) => {
  const match = pathname.match(/^\/([a-z]{2})\//);
  return match ? match[1] : 'et'; // Default to Estonian
};

// Create language-aware URL
const createLanguageUrl = (to, currentLanguage) => {
  // If the 'to' already has a language prefix, use it as is
  if (to.match(/^\/[a-z]{2}\//)) {
    return to;
  }
  
  // If it's a root path, just add language
  if (to === '/') {
    return `/${currentLanguage}`;
  }
  
  // Add language prefix to the path
  const cleanPath = to.startsWith('/') ? to : `/${to}`;
  return `/${currentLanguage}${cleanPath}`;
};

// Language-aware Link component
const LanguageAwareLink = ({ to, children, className, ...props }) => {
  const location = useLocation();
  const currentLanguage = getCurrentLanguage(location.pathname);
  const languageAwareUrl = createLanguageUrl(to, currentLanguage);

  return (
    <Link to={languageAwareUrl} className={className} {...props}>
      {children}
    </Link>
  );
};

export default LanguageAwareLink;
export { getCurrentLanguage, createLanguageUrl, SUPPORTED_LANGUAGES };
