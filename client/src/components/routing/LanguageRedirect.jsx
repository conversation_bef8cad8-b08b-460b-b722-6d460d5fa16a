import React, { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";

const SUPPORTED_LANGUAGES = ["en", "et", "fi", "de", "sv"];
const DEFAULT_LANGUAGE = "et";

const LanguageRedirect = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { i18n } = useTranslation();

  useEffect(() => {
    // Get user's preferred language
    const browserLang = navigator.language.split("-")[0];
    const storedLang = localStorage.getItem("i18nextLng");

    // Determine the best language to use
    const preferredLang = SUPPORTED_LANGUAGES.includes(storedLang)
      ? storedLang
      : SUPPORTED_LANGUAGES.includes(browserLang)
      ? browserLang
      : DEFAULT_LANGUAGE;

    // Set the language in i18next
    if (i18n.language !== preferredLang) {
      i18n.changeLanguage(preferredLang);
    }

    // Get current path and add language prefix
    const currentPath = location.pathname;
    const searchParams = location.search;

    // If we're on root, redirect to language home
    if (currentPath === "/") {
      navigate(`/${preferredLang}${searchParams}`, { replace: true });
    } else {
      // For other paths, add language prefix
      navigate(`/${preferredLang}${currentPath}${searchParams}`, {
        replace: true,
      });
    }
  }, [navigate, location, i18n]);

  // Don't render children during redirect
  return null;
};

export default LanguageRedirect;
