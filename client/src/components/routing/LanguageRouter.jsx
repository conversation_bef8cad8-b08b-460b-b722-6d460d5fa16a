import React, { useEffect } from "react";
import { usePara<PERSON>, useNavigate, useLocation, Outlet } from "react-router-dom";
import { useTranslation } from "react-i18next";

// Supported languages
export const SUPPORTED_LANGUAGES = ["en", "et", "fi", "de", "sv"];
export const DEFAULT_LANGUAGE = "et";

// Language-aware wrapper component
const LanguageRouter = () => {
  const { lang } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { i18n } = useTranslation();

  useEffect(() => {
    // Check if we have a language in the URL
    const pathLang = lang;

    // If no language in URL or invalid language, redirect to default
    if (!pathLang || !SUPPORTED_LANGUAGES.includes(pathLang)) {
      // Get user's preferred language from browser or localStorage
      const browserLang = navigator.language.split("-")[0];
      const storedLang = localStorage.getItem("i18nextLng");
      const preferredLang = SUPPORTED_LANGUAGES.includes(storedLang)
        ? storedLang
        : SUPPORTED_LANGUAGES.includes(browserLang)
        ? browserLang
        : DEFAULT_LANGUAGE;

      // Redirect to language-prefixed URL
      const pathWithoutLang =
        location.pathname.replace(/^\/[a-z]{2}/, "") || "/";
      const newPath = `/${preferredLang}${pathWithoutLang}${location.search}`;
      navigate(newPath, { replace: true });
      return;
    }

    // Update i18n language if it's different
    if (i18n.language !== pathLang) {
      i18n.changeLanguage(pathLang);
    }
  }, [lang, location, navigate, i18n]);

  // Don't render children until language is properly set
  if (!lang || !SUPPORTED_LANGUAGES.includes(lang)) {
    return null;
  }

  return <Outlet />;
};

// Hook to get current language from URL
export const useCurrentLanguage = () => {
  const { lang } = useParams();
  return lang || DEFAULT_LANGUAGE;
};

// Hook to generate language-aware URLs
export const useLanguageUrl = () => {
  const currentLang = useCurrentLanguage();

  const getUrl = (path, language = currentLang) => {
    // Remove leading slash if present
    const cleanPath = path.startsWith("/") ? path.slice(1) : path;
    return `/${language}/${cleanPath}`;
  };

  const getAllLanguageUrls = (path) => {
    const cleanPath = path.startsWith("/") ? path.slice(1) : path;
    return SUPPORTED_LANGUAGES.reduce((urls, lang) => {
      urls[lang] = `/${lang}/${cleanPath}`;
      return urls;
    }, {});
  };

  return { getUrl, getAllLanguageUrls, currentLang };
};

export default LanguageRouter;
