import React, { useEffect, useState } from "react";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import CodeBlockLowlight from "@tiptap/extension-code-block-lowlight";
import Code from "@tiptap/extension-code";

const TipTapEditor = ({
  content,
  onChange,
  placeholder = "Start writing...",
}) => {
  const [lowlight, setLowlight] = useState(null);
  const [isHighlightLoaded, setIsHighlightLoaded] = useState(false);

  // Load real lowlight library for TipTap compatibility
  useEffect(() => {
    const loadLowlight = async () => {
      try {
        // Import real lowlight library with common languages
        const { createLowlight, common } = await import("lowlight");
        
        // Create lowlight instance with common languages (includes JS, TS, Python, CSS, HTML, JSON, etc.)
        const lowlightInstance = createLowlight(common);
        
        setLowlight(lowlightInstance);
        setIsHighlightLoaded(true);
      } catch (error) {
        console.warn("Failed to load lowlight for editor:", error);
        setIsHighlightLoaded(true); // Allow editor to work without highlighting
      }
    };

    loadLowlight();
  }, []);


  // Create editor with conditional CodeBlockLowlight extension
  const editor = useEditor(
    {
      extensions: [
        StarterKit.configure({
          code: false, // Disable default code extension
          codeBlock: false, // Disable default codeBlock extension
        }),
        Code.configure({
          HTMLAttributes: {
            class: "inline-code",
          },
        }),
        // Only add CodeBlockLowlight if we have a valid lowlight instance
        ...(isHighlightLoaded && lowlight
          ? [
              CodeBlockLowlight.configure({
                lowlight,
                defaultLanguage: "javascript",
                HTMLAttributes: {
                  class: "code-block",
                },
              }),
            ]
          : []),
      ],
      content: content,
      onUpdate: ({ editor }) => {
        const html = editor.getHTML();
        onChange(html);
      },
      editorProps: {
        attributes: {
          class: "tiptap-editor",
        },
      },
    },
    [lowlight, isHighlightLoaded, content]
  );

  // Update editor content when the content prop changes
  useEffect(() => {
    if (editor && content !== undefined && content !== editor.getHTML()) {
      editor.commands.setContent(content);
    }
  }, [editor, content]);

  if (!editor || !isHighlightLoaded) {
    return (
      <div className="tiptap-wrapper">
        <div className="tiptap-loading">
          <p>Loading editor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="tiptap-wrapper">
      {/* Toolbar */}
      <div className="tiptap-toolbar">
        <div className="toolbar-group">
          <button
            type="button"
            onClick={() => editor.chain().focus().toggleBold().run()}
            className={`toolbar-btn ${editor.isActive("bold") ? "active" : ""}`}
            title="Bold"
          >
            <strong>B</strong>
          </button>
          <button
            type="button"
            onClick={() => editor.chain().focus().toggleItalic().run()}
            className={`toolbar-btn ${editor.isActive("italic") ? "active" : ""}`}
            title="Italic"
          >
            <em>I</em>
          </button>
          <button
            type="button"
            onClick={() => editor.chain().focus().toggleCode().run()}
            className={`toolbar-btn ${editor.isActive("code") ? "active" : ""}`}
            title="Inline Code"
          >
            {"</>"}
          </button>
        </div>

        <div className="toolbar-group">
          <button
            type="button"
            onClick={() =>
              editor.chain().focus().toggleHeading({ level: 1 }).run()
            }
            className={`toolbar-btn ${
              editor.isActive("heading", { level: 1 }) ? "active" : ""
            }`}
            title="Heading 1"
          >
            H1
          </button>
          <button
            type="button"
            onClick={() =>
              editor.chain().focus().toggleHeading({ level: 2 }).run()
            }
            className={`toolbar-btn ${
              editor.isActive("heading", { level: 2 }) ? "active" : ""
            }`}
            title="Heading 2"
          >
            H2
          </button>
          <button
            type="button"
            onClick={() =>
              editor.chain().focus().toggleHeading({ level: 3 }).run()
            }
            className={`toolbar-btn ${
              editor.isActive("heading", { level: 3 }) ? "active" : ""
            }`}
            title="Heading 3"
          >
            H3
          </button>
        </div>

        <div className="toolbar-group">
          <button
            type="button"
            onClick={() => editor.chain().focus().toggleBulletList().run()}
            className={`toolbar-btn ${editor.isActive("bulletList") ? "active" : ""}`}
            title="Bullet List"
          >
            • List
          </button>
          <button
            type="button"
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
            className={`toolbar-btn ${editor.isActive("orderedList") ? "active" : ""}`}
            title="Numbered List"
          >
            1. List
          </button>
          <button
            type="button"
            onClick={() => editor.chain().focus().toggleCodeBlock().run()}
            className={`toolbar-btn ${editor.isActive("codeBlock") ? "active" : ""}`}
            title="Code Block"
          >
            {"{ }"}
          </button>
        </div>

        <div className="toolbar-group">
          <button
            type="button"
            onClick={() => editor.chain().focus().toggleBlockquote().run()}
            className={`toolbar-btn ${editor.isActive("blockquote") ? "active" : ""}`}
            title="Quote"
          >
            " Quote
          </button>
          <button
            type="button"
            onClick={() => editor.chain().focus().setHorizontalRule().run()}
            className="toolbar-btn"
            title="Horizontal Rule"
          >
            ―
          </button>
        </div>

        <div className="toolbar-group">
          <button
            type="button"
            onClick={() => editor.chain().focus().undo().run()}
            disabled={!editor.can().undo()}
            className="toolbar-btn"
            title="Undo"
          >
            ↶
          </button>
          <button
            type="button"
            onClick={() => editor.chain().focus().redo().run()}
            disabled={!editor.can().redo()}
            className="toolbar-btn"
            title="Redo"
          >
            ↷
          </button>
        </div>
      </div>

      {/* Editor */}
      <EditorContent
        editor={editor}
        className="tiptap-content"
        placeholder={placeholder}
      />
    </div>
  );
};

export default TipTapEditor;
