import React, { useEffect, useState } from "react";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import CodeBlockLowlight from "@tiptap/extension-code-block-lowlight";
import Code from "@tiptap/extension-code";

const TipTapEditor = ({
  content,
  onChange,
  placeholder = "Start writing...",
}) => {
  const [lowlight, setLowlight] = useState(null);
  const [isHighlightLoaded, setIsHighlightLoaded] = useState(false);

  // Load real lowlight library for TipTap compatibility
  useEffect(() => {
    const loadLowlight = async () => {
      try {
        // Import real lowlight library with common languages
        const { createLowlight, common } = await import("lowlight");
        const lowlightInstance = createLowlight(common);
        
        console.log("Lowlight loaded successfully:", lowlightInstance);
        setLowlight(lowlightInstance);
        setIsHighlightLoaded(true);
      } catch (error) {
        console.warn("Failed to load lowlight for editor:", error);
        setIsHighlightLoaded(true); // Allow editor to work without highlighting
      }
    };

    loadLowlight();
  }, []);

  console.log("TipTap render:", { isHighlightLoaded, hasLowlight: !!lowlight });

  // Create editor with conditional CodeBlockLowlight extension
  const editor = useEditor(
    {
      extensions: [
        StarterKit.configure({
          code: false, // Disable default code extension
          codeBlock: false, // Disable default codeBlock extension
        }),
        Code.configure({
          HTMLAttributes: {
            class: "inline-code",
          },
        }),
        // Only add CodeBlockLowlight if we have a valid lowlight instance
        ...(isHighlightLoaded && lowlight
          ? [
              CodeBlockLowlight.configure({
                lowlight,
                defaultLanguage: "javascript",
                HTMLAttributes: {
                  class: "code-block",
                },
              }),
            ]
          : []),
      ],
      content: content,
      onUpdate: ({ editor }) => {
        const html = editor.getHTML();
        onChange(html);
      },
      editorProps: {
        attributes: {
          class: "tiptap-editor",
        },
      },
    },
    [lowlight, isHighlightLoaded, content]
  );

  // Update editor content when the content prop changes
  useEffect(() => {
    if (editor && content !== undefined && content !== editor.getHTML()) {
      editor.commands.setContent(content);
    }
  }, [editor, content]);

  if (!editor || !isHighlightLoaded) {
    return (
      <div className="tiptap-wrapper">
        <div className="tiptap-loading">
          <p>Loading editor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="tiptap-wrapper">
      <div className="tiptap-toolbar">
        <div className="toolbar-group">
          <button
            type="button"
            onClick={() => editor.chain().focus().toggleBold().run()}
            className={editor.isActive("bold") ? "is-active" : ""}
            title="Bold"
          >
            <strong>B</strong>
          </button>
          <button
            type="button"
            onClick={() => editor.chain().focus().toggleItalic().run()}
            className={editor.isActive("italic") ? "is-active" : ""}
            title="Italic"
          >
            <em>I</em>
          </button>
        </div>
      </div>
      <EditorContent
        editor={editor}
        className="tiptap-content"
        placeholder={placeholder}
      />
    </div>
  );
};

export default TipTapEditor;
