import React from "react";
import PropTypes from "prop-types";
import { useTranslation } from "react-i18next";

export default function Comments({ comments = [] }) {
  const { t } = useTranslation();
  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Show empty state if no comments
  if (!comments || comments.length === 0) {
    return (
      <div className="text-center py-4">
        <p className="text-muted">{t("blog.comments.no_comments")}</p>
      </div>
    );
  }
  return (
    <>
      {comments.map((comment, i) => (
        <li key={i} className="media comment-item">
          {/* <a className="float-start" href="#">
            <img
              className="media-object comment-avatar"
              src={comment.avatar}
              alt=""
              width={50}
              height={50}
            />
          </a> */}
          <div className="media-body">
            <div className="comment-item-data">
              <div className="comment-author">
                <a href="#">{comment.author}</a>
              </div>
              {formatDate(comment.createdAt)}{" "}
              <span className="separator">—</span>
              <a href="#">
                <i className="fa fa-comment" />
                &nbsp;{t("blog.comments.reply")}
              </a>
            </div>
            <p>{comment.content}</p>
            {comment.replies &&
              comment.replies.length > 0 &&
              comment.replies.map((reply) => (
                <div key={reply.id} className="media comment-item">
                  <a className="float-start" href="#">
                    <img
                      className="media-object comment-avatar"
                      src="/assets/images/user-avatar.png"
                      alt=""
                      width={100}
                      height={100}
                    />
                  </a>
                  <div className="media-body">
                    <div className="comment-item-data">
                      <div className="comment-author">
                        <a href="#">{reply.author}</a>
                      </div>
                      {formatDate(reply.createdAt)}{" "}
                      <span className="separator">—</span>
                      <a href="#">
                        <i className="fa fa-comment" />
                        &nbsp;{t("blog.comments.reply")}
                      </a>
                    </div>
                    <p>{reply.content}</p>
                  </div>
                </div>
              ))}
          </div>
        </li>
      ))}
    </>
  );
}

Comments.propTypes = {
  comments: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      author: PropTypes.string.isRequired,
      content: PropTypes.string.isRequired,
      createdAt: PropTypes.string.isRequired,
      replies: PropTypes.arrayOf(
        PropTypes.shape({
          id: PropTypes.string.isRequired,
          author: PropTypes.string.isRequired,
          content: PropTypes.string.isRequired,
          createdAt: PropTypes.string.isRequired,
        })
      ),
    })
  ),
};
