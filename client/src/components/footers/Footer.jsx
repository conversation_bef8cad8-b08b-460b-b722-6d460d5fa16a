import { socialMediaLinks } from "@/data/footer";
import React from "react";
import { useTranslation } from "react-i18next";

export default function Footer() {
  const { t } = useTranslation();

  const scrollToTop = (event) => {
    event.preventDefault();
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  return (
    <div className="container position-relative text-center pt-140 pb-80 pb-sm-50">
      {/* Scroll Up */}
      <div className="local-scroll link-to-top-2-wrap">
        <a href="#top" className="link-to-top-2" onClick={scrollToTop}>
          {t("footer.backToTop")}
        </a>
      </div>
      {/* End Scroll Up */}

      <div className="footer-social-links mb-60">
        {socialMediaLinks.map((elm, i) => (
          <a
            href={elm.href}
            key={i}
            title={elm.title}
            rel="noreferrer nofollow"
            target="_blank"
          >
            <span className="visually-hidden">{elm.name}</span>
            <i className={`fa ${elm.iconClass}`} />
          </a>
        ))}
      </div>

      {/* Footer Text */}
      <div className="footer-text">
        {/* Copyright */}
        <div>
          © Devskills OÜ {new Date().getFullYear()} -{" "}
          {t("footer.allRightsReserved")}
        </div>
        {/* End Copyright */}
        <div className="footer-made">
          Making great things that people actually need
        </div>
      </div>
      {/* End Footer Text */}
    </div>
  );
}
