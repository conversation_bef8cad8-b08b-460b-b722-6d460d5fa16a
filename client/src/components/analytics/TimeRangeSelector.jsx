import React, { useState, useRef, useEffect } from "react";
import PropTypes from "prop-types";

const TimeRangeSelector = ({ options, value, onChange, comparedPeriod }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const selectedOption = options.find((option) => option.value === value);

  const handleOptionClick = (optionValue) => {
    onChange(optionValue);
    setIsOpen(false);
  };

  return (
    <div className="time-range-selector-wrapper d-flex align-items-center gap-3">
      {/* Time Range Dropdown */}
      <div className="position-relative" ref={dropdownRef}>
        <button
          className="btn btn-outline-secondary d-flex align-items-center gap-2 text-nowrap"
          onClick={() => setIsOpen(!isOpen)}
          aria-expanded={isOpen}
        >
          <iconify-icon
            icon="solar:calendar-bold"
            className="text-primary"
          ></iconify-icon>
          <span className="text-nowrap">
            {selectedOption?.label || "Select time range"}
          </span>
          <iconify-icon
            icon="solar:alt-arrow-down-bold"
            className={`transition-transform ${isOpen ? "rotate-180" : ""}`}
          ></iconify-icon>
        </button>

        {isOpen && (
          <div className="dropdown-menu show position-absolute mt-1 shadow-lg border-0 bg-white rounded-3">
            {options.map((option) => (
              <button
                key={option.value}
                className={`dropdown-item px-3 py-2 text-nowrap ${
                  option.value === value
                    ? "active bg-primary text-white"
                    : "text-dark"
                }`}
                onClick={() => handleOptionClick(option.value)}
              >
                {option.label}
              </button>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

TimeRangeSelector.propTypes = {
  options: PropTypes.arrayOf(
    PropTypes.shape({
      value: PropTypes.string.isRequired,
      label: PropTypes.string.isRequired,
    })
  ).isRequired,
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  comparedPeriod: PropTypes.string,
};

export default TimeRangeSelector;
