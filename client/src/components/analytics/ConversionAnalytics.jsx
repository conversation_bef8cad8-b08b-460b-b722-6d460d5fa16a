import React, { useState, useEffect } from "react";
import { adminAPI } from "../../utils/api";

export default function ConversionAnalytics({ timeRange, selectedLanguage }) {
  const [conversionData, setConversionData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    loadConversionData();
  }, [timeRange, selectedLanguage]);

  const loadConversionData = async () => {
    try {
      setLoading(true);
      setError("");

      // Check if user is authenticated
      const token = localStorage.getItem("adminToken");
      if (!token) {
        setError("Authentication required. Please log in to access this page.");
        setLoading(false);
        return;
      }

      const end = new Date();
      let start = new Date();

      switch (timeRange) {
        case "last7days":
          start.setDate(end.getDate() - 7);
          break;
        case "last30days":
          start.setDate(end.getDate() - 30);
          break;
        case "last90days":
          start.setDate(end.getDate() - 90);
          break;
        default:
          start.setDate(end.getDate() - 30);
      }

      const result = await adminAPI.getConversionAnalytics(
        start.toISOString().split("T")[0],
        end.toISOString().split("T")[0],
        selectedLanguage
      );

      // Handle response like blog analytics
      if (result.response.ok && result.data) {
        setConversionData(result.data.data || result.data);
      } else {
        console.error(
          "Conversion API failed:",
          result.response.status,
          result.response.statusText
        );
        if (result.response.status === 401 || result.response.status === 403) {
          setError("Authentication failed. Please log in again.");
          localStorage.removeItem("adminToken");
          return;
        }
        setError("Failed to load conversion data");
      }
    } catch (err) {
      console.error("Error loading conversion analytics:", err);
      setError("Error loading conversion analytics");
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="card border-0 shadow-sm">
        <div className="card-body p-4">
          <h5 className="card-title mb-4">Conversion Analytics</h5>
          <div className="d-flex justify-content-center py-5">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="card border-0 shadow-sm">
        <div className="card-body p-4">
          <h5 className="card-title mb-4">Conversion Analytics</h5>
          <div className="alert alert-danger" role="alert">
            <iconify-icon
              icon="solar:danger-bold"
              className="me-2"
            ></iconify-icon>
            {error}
          </div>
        </div>
      </div>
    );
  }

  if (!conversionData) {
    return (
      <div className="card border-0 shadow-sm">
        <div className="card-body p-4">
          <h5 className="card-title mb-4">Conversion Analytics</h5>
          <div className="text-center py-5 text-muted">
            <iconify-icon
              icon="solar:target-bold"
              className="fs-1 mb-3 d-block"
            ></iconify-icon>
            No conversion data available for the selected time range.
          </div>
        </div>
      </div>
    );
  }

  const { summary, conversions_by_source, recent_events } = conversionData;

  console.log("🎨 Rendering ConversionAnalytics with data:", {
    summary,
    conversions_by_source,
    recent_events,
  });

  return (
    <div className="card border-0 shadow-sm">
      <div className="card-body p-4">
        <div className="d-flex align-items-center justify-content-between mb-4">
          <h5 className="card-title mb-0">Conversion Analytics</h5>
          <span className="text-primary small">
            {summary.total_conversions} conversions • €{summary.total_value}{" "}
            total value
          </span>
        </div>

        {/* Summary Cards */}
        <div className="row mb-4">
          <div className="col-sm-6 col-lg-3 mb-3">
            <div className="card border-0 shadow-sm">
              <div className="card-body">
                <div className="d-flex align-items-center">
                  <div className="flex-shrink-0 me-3">
                    <div
                      className="bg-primary rounded-circle d-flex align-items-center justify-content-center"
                      style={{ width: "40px", height: "40px" }}
                    >
                      <iconify-icon
                        icon="solar:target-bold"
                        className="text-white"
                      ></iconify-icon>
                    </div>
                  </div>
                  <div>
                    <p className="text-muted mb-1 small">Total Conversions</p>
                    <h4 className="mb-0">{summary.total_conversions}</h4>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="col-sm-6 col-lg-3 mb-3">
            <div className="card border-0 shadow-sm">
              <div className="card-body">
                <div className="d-flex align-items-center">
                  <div className="flex-shrink-0 me-3">
                    <div
                      className="bg-success rounded-circle d-flex align-items-center justify-content-center"
                      style={{ width: "40px", height: "40px" }}
                    >
                      <iconify-icon
                        icon="solar:euro-bold"
                        className="text-white"
                      ></iconify-icon>
                    </div>
                  </div>
                  <div>
                    <p className="text-muted mb-1 small">Total Value</p>
                    <h4 className="mb-0">€{summary.total_value}</h4>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="col-sm-6 col-lg-3 mb-3">
            <div className="card border-0 shadow-sm">
              <div className="card-body">
                <div className="d-flex align-items-center">
                  <div className="flex-shrink-0 me-3">
                    <div
                      className="bg-info rounded-circle d-flex align-items-center justify-content-center"
                      style={{ width: "40px", height: "40px" }}
                    >
                      <iconify-icon
                        icon="solar:chart-bold"
                        className="text-white"
                      ></iconify-icon>
                    </div>
                  </div>
                  <div>
                    <p className="text-muted mb-1 small">Average Value</p>
                    <h4 className="mb-0">€{summary.average_value}</h4>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="col-sm-6 col-lg-3 mb-3">
            <div className="card border-0 shadow-sm">
              <div className="card-body">
                <div className="d-flex align-items-center">
                  <div className="flex-shrink-0 me-3">
                    <div
                      className="bg-warning rounded-circle d-flex align-items-center justify-content-center"
                      style={{ width: "40px", height: "40px" }}
                    >
                      <iconify-icon
                        icon="solar:layers-bold"
                        className="text-white"
                      ></iconify-icon>
                    </div>
                  </div>
                  <div>
                    <p className="text-muted mb-1 small">Sources</p>
                    <h4 className="mb-0">{conversions_by_source.length}</h4>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Conversions by Source Table */}
        <div className="card mb-4">
          <div className="card-header">
            <h5 className="card-title mb-0">Conversions by Source</h5>
          </div>
          <div className="card-body p-0">
            <div className="table-responsive">
              <table className="table table-hover mb-0">
                <thead className="table-light">
                  <tr>
                    <th>Source</th>
                    <th>Conversions</th>
                    <th>Value</th>
                    <th>Top CTA Type</th>
                    <th>Languages</th>
                  </tr>
                </thead>
                <tbody>
                  {conversions_by_source.map((source, index) => {
                    const topCtaType = Object.entries(source.cta_types).sort(
                      (a, b) => b[1] - a[1]
                    )[0];
                    const topLanguages = Object.entries(source.languages)
                      .sort((a, b) => b[1] - a[1])
                      .slice(0, 2);

                    return (
                      <tr key={index}>
                        <td>
                          <strong>
                            {source.source
                              .replace(/_/g, " ")
                              .replace(/\b\w/g, (l) => l.toUpperCase())}
                          </strong>
                        </td>
                        <td>{source.total_conversions}</td>
                        <td>€{source.total_value}</td>
                        <td>
                          {topCtaType
                            ? `${topCtaType[0]} (${topCtaType[1]})`
                            : "-"}
                        </td>
                        <td>
                          {topLanguages
                            .map(
                              ([lang, count]) =>
                                `${lang.toUpperCase()} (${count})`
                            )
                            .join(", ")}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Recent Conversions Table */}
        <div className="card mb-4">
          <div className="card-header">
            <h5 className="card-title mb-0">Recent Conversions</h5>
          </div>
          <div className="card-body p-0">
            <div className="table-responsive">
              <table className="table table-hover mb-0">
                <thead className="table-light">
                  <tr>
                    <th>Timestamp</th>
                    <th>Source</th>
                    <th>CTA Type</th>
                    <th>Language</th>
                    <th>Value</th>
                  </tr>
                </thead>
                <tbody>
                  {recent_events.slice(0, 10).map((event, index) => (
                    <tr key={index}>
                      <td>
                        <small>
                          {new Date(event.timestamp).toLocaleString()}
                        </small>
                      </td>
                      <td>
                        {event.source
                          .replace(/_/g, " ")
                          .replace(/\b\w/g, (l) => l.toUpperCase())}
                      </td>
                      <td>
                        <span className="badge bg-secondary">
                          {event.cta_type
                            .replace(/_/g, " ")
                            .replace(/\b\w/g, (l) => l.toUpperCase())}
                        </span>
                      </td>
                      <td>
                        <span className="badge bg-primary">
                          {event.language.toUpperCase()}
                        </span>
                      </td>
                      <td>€{event.value}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
