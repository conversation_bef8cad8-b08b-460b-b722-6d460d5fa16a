import React, { useState, useEffect } from "react";
import { adminAPI } from "../../utils/api";

export default function StaticPagesAnalytics({ timeRange, selectedLanguage }) {
  const [pagesData, setPagesData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    loadPagesData();
  }, [timeRange, selectedLanguage]);

  const loadPagesData = async () => {
    try {
      setLoading(true);
      setError("");

      // Check if user is authenticated
      const token = localStorage.getItem("adminToken");
      if (!token) {
        setError("Authentication required. Please log in to access this page.");
        setLoading(false);
        return;
      }

      const result = await adminAPI.getStaticPagesAnalytics(
        timeRange,
        selectedLanguage
      );

      // Handle response like blog analytics
      if (result.response.ok && result.data) {
        setPagesData(result.data.data || result.data);
      } else {
        console.error(
          "Static pages API failed:",
          result.response.status,
          result.response.statusText
        );
        if (result.response.status === 401 || result.response.status === 403) {
          setError("Authentication failed. Please log in again.");
          localStorage.removeItem("adminToken");
          return;
        }
        setError("Failed to load static pages data");
      }
    } catch (err) {
      console.error("Error loading static pages analytics:", err);
      setError("Error loading static pages analytics");
    } finally {
      setLoading(false);
    }
  };

  const getPageTypeColor = (pageType) => {
    const colors = {
      home: "bg-primary",
      about: "bg-success",
      services: "bg-info",
      products: "bg-warning",
      contact: "bg-danger",
      blog: "bg-secondary",
      other: "bg-dark",
    };
    return colors[pageType] || colors.other;
  };

  const formatPageTitle = (path) => {
    const segments = path.split("/").filter(Boolean);
    if (segments.length === 0) return "Home";
    if (segments.length === 1) return segments[0];

    const pagePath = segments.slice(1).join(" / ");
    return pagePath.replace(/-/g, " ").replace(/\b\w/g, function (l) {
      return l.toUpperCase();
    });
  };

  if (loading) {
    return (
      <div className="card border-0 shadow-sm">
        <div className="card-body p-4">
          <h5 className="card-title mb-4">Static Pages Analytics</h5>
          <div className="d-flex justify-content-center py-5">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="card border-0 shadow-sm">
        <div className="card-body p-4">
          <h5 className="card-title mb-4">Static Pages Analytics</h5>
          <div className="alert alert-danger" role="alert">
            <iconify-icon
              icon="solar:danger-bold"
              className="me-2"
            ></iconify-icon>
            {error}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="card border-0 shadow-sm">
      <div className="card-body p-4">
        <div className="d-flex align-items-center justify-content-between mb-4">
          <h5 className="card-title mb-0">Static Pages Analytics</h5>
          <span className="text-primary small">
            {pagesData.length} pages tracked
          </span>
        </div>

        <div className="table-responsive">
          <table className="table table-hover align-middle">
            <thead className="table-light">
              <tr>
                <th>Page</th>
                <th>Type</th>
                <th>Views</th>
                <th>Unique Visitors</th>
                <th>Languages</th>
              </tr>
            </thead>
            <tbody>
              {pagesData.map((page, index) => (
                <tr key={index}>
                  <td>
                    <div>
                      <div className="fw-bold">
                        {page.page_title || formatPageTitle(page.path)}
                      </div>
                      <small className="text-muted">{page.path}</small>
                    </div>
                  </td>
                  <td>
                    <span
                      className={`badge ${getPageTypeColor(
                        page.page_type
                      )} text-white`}
                    >
                      {page.page_type}
                    </span>
                  </td>
                  <td>{page.views.toLocaleString()}</td>
                  <td>{page.unique_visitors.toLocaleString()}</td>
                  <td>
                    <small>
                      {page.languages
                        ? Object.entries(page.languages)
                            .map(
                              ([lang, count]) =>
                                `${lang.toUpperCase()} (${count})`
                            )
                            .join(", ")
                        : "-"}
                    </small>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {pagesData.length === 0 && (
          <div className="text-center py-4">
            <p className="text-muted mb-0">
              No page data available for the selected time range.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
