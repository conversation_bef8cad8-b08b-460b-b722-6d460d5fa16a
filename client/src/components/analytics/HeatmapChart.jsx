import React from "react";
import PropTypes from "prop-types";

const HeatmapChart = ({ data, title }) => {
  if (!data || !data.heatmapData) {
    return (
      <div className="card border-0 shadow-sm h-100">
        <div className="card-body p-4">
          <div className="d-flex align-items-center justify-content-between mb-4">
            <h6 className="card-title mb-0">{title}</h6>
            <a
              href="https://analytics.google.com"
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary small text-decoration-none"
            >
              View Report
            </a>
          </div>
          <div
            className="d-flex align-items-center justify-content-center"
            style={{ height: "250px" }}
          >
            <div className="text-center text-muted">
              <iconify-icon
                icon="solar:chart-square-bold"
                className="fs-1 mb-3 d-block"
              ></iconify-icon>
              <p className="mb-0 small">No heatmap data available</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const days = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
  const hours = Array.from({ length: 24 }, (_, i) => {
    const hour = i === 0 ? 12 : i > 12 ? i - 12 : i;
    const period = i < 12 ? "AM" : "PM";
    return `${hour} ${period}`;
  });

  // Get the maximum value for normalization
  const maxValue = Math.max(...data.heatmapData.flat());

  // Generate intensity levels (0-4 scale like GitHub)
  const getIntensity = (value) => {
    if (value === 0) return 0;
    const percentage = value / maxValue;
    if (percentage <= 0.25) return 1;
    if (percentage <= 0.5) return 2;
    if (percentage <= 0.75) return 3;
    return 4;
  };

  const getIntensityColor = (intensity) => {
    const colors = {
      0: "#f3f4f6", // gray-100
      1: "#dbeafe", // blue-100
      2: "#93c5fd", // blue-300
      3: "#3b82f6", // blue-500
      4: "#1d4ed8", // blue-700
    };
    return colors[intensity] || colors[0];
  };

  return (
    <div className="card border-0 shadow-sm h-100">
      <div className="card-body p-4">
        <div className="d-flex align-items-center justify-content-between mb-4">
          <h6 className="card-title mb-0">{title}</h6>
          <a href="#" className="text-primary small text-decoration-none">
            View Report
          </a>
        </div>

        <div className="heatmap-container">
          {/* Time labels */}
          <div className="heatmap-time-labels mb-2">
            <div></div> {/* Empty space for day labels column */}
            <div className="heatmap-time-row">
              <div className="heatmap-time-label">12 AM</div>
              <div className="heatmap-time-label">6 AM</div>
              <div className="heatmap-time-label">12 PM</div>
              <div className="heatmap-time-label">6 PM</div>
              <div className="heatmap-time-label">12 AM</div>
            </div>
          </div>

          {/* Heatmap grid */}
          <div className="heatmap-grid-container">
            {days.map((day, dayIndex) => (
              <div key={day} className="heatmap-row">
                {/* Day label */}
                <div className="heatmap-day-label">{day}</div>

                {/* Hour cells */}
                <div className="heatmap-cells">
                  {Array.from({ length: 24 }, (_, hourIndex) => {
                    const value = data.heatmapData[dayIndex]?.[hourIndex] || 0;
                    const intensity = getIntensity(value);

                    return (
                      <div
                        key={hourIndex}
                        className="heatmap-cell"
                        style={{
                          backgroundColor: getIntensityColor(intensity),
                        }}
                        title={`${day} ${hours[hourIndex]}: ${value} views`}
                        onMouseEnter={(e) => {
                          e.target.style.transform = "scale(1.2)";
                          e.target.style.zIndex = "10";
                        }}
                        onMouseLeave={(e) => {
                          e.target.style.transform = "scale(1)";
                          e.target.style.zIndex = "1";
                        }}
                      />
                    );
                  })}
                </div>
              </div>
            ))}
          </div>

          {/* Legend */}
          <div className="d-flex align-items-center justify-content-between mt-3">
            <span className="text-muted small">Less</span>
            <div className="d-flex gap-1">
              {[0, 1, 2, 3, 4].map((intensity) => (
                <div
                  key={intensity}
                  style={{
                    width: "12px",
                    height: "12px",
                    backgroundColor: getIntensityColor(intensity),
                    borderRadius: "2px",
                  }}
                />
              ))}
            </div>
            <span className="text-muted small">More</span>
          </div>
        </div>
      </div>
    </div>
  );
};

HeatmapChart.propTypes = {
  data: PropTypes.shape({
    heatmapData: PropTypes.arrayOf(
      PropTypes.shape({
        hour: PropTypes.number,
        day: PropTypes.number,
        value: PropTypes.number,
      })
    ),
  }),
  title: PropTypes.string,
};

export default HeatmapChart;
