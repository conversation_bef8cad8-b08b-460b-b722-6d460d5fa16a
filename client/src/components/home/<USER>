import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { blogAPI } from "@/utils/api";
import { useTranslation } from "react-i18next";

export default function Blog() {
  const { i18n, t } = useTranslation();
  const currentLanguage = i18n.language || "et";
  const [blogPosts, setBlogPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    const fetchBlogPosts = async () => {
      try {
        setLoading(true);
        const result = await blogAPI.getFeaturedPosts(currentLanguage, 3);

        if (result.response.ok && result.data) {
          // Extract the posts array from the nested response structure
          const posts = result.data.data?.data || result.data.data || [];
          console.log("Blog API response:", result.data);
          console.log("Posts array:", posts);
          setBlogPosts(Array.isArray(posts) ? posts : []);
        } else {
          console.error("Failed to fetch blog posts:", result.response.status);
          setBlogPosts([]);
        }
      } catch (error) {
        console.error("Error fetching blog posts:", error);
        setError("Failed to load blog posts");
        setBlogPosts([]);
      } finally {
        setLoading(false);
      }
    };

    fetchBlogPosts();
  }, [currentLanguage]);

  // Helper function to get translation for current language
  const getTranslation = (post, field) => {
    const translation = post.translations?.find(
      (t) => t.language === currentLanguage
    );
    return (
      translation?.[field] ||
      post.translations?.find((t) => t.language === "en")?.[field] ||
      ""
    );
  };

  // Show loading state
  if (loading) {
    return (
      <div className="container">
        <div className="row mb-70 mb-sm-50">
          <div className="col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center">
            <h2 className="section-title mb-30 mb-sm-20">
              <span className="text-gray">{t("blog.title")}</span>
              <span className="text-gray">.</span>
            </h2>
            <div className="text-gray">{t("blog.subtitle")}</div>
          </div>
        </div>
        <div className="row mt-n30">
          <div className="col-12 text-center">
            <div className="text-gray">Loading blog posts...</div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="container">
        <div className="row mb-70 mb-sm-50">
          <div className="col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center">
            <h2 className="section-title mb-30 mb-sm-20">
              <span className="text-gray">{t("blog.title")}</span>
              <span className="text-gray">.</span>
            </h2>
            <div className="text-gray">{t("blog.subtitle")}</div>
          </div>
        </div>
        <div className="row mt-n30">
          <div className="col-12 text-center">
            <div className="text-gray">{error}</div>
          </div>
        </div>
      </div>
    );
  }

  // Show empty state if no posts
  if (blogPosts.length === 0) {
    return (
      <div className="container">
        <div className="row mb-70 mb-sm-50">
          <div className="col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center">
            <h2 className="section-title mb-30 mb-sm-20">
              <span className="text-gray">{t("blog.title")}</span>
              <span className="text-gray">.</span>
            </h2>
            <div className="text-gray">{t("blog.subtitle")}</div>
          </div>
        </div>
        <div className="row mt-n30">
          <div className="col-12 text-center">
            <div className="text-gray">No blog posts available yet.</div>
          </div>
        </div>
      </div>
    );
  }
  return (
    <div className="container">
      <div className="row mb-70 mb-sm-50">
        <div className="col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center">
          <h2 className="section-title mb-30 mb-sm-20">
            <span className="text-gray">{t("blog.title")}</span>
            <span className="text-gray">.</span>
          </h2>
          <div className="text-gray">{t("blog.subtitle")}</div>
        </div>
        <div className="col-md-2 col-lg-3 text-center text-md-end mt-10 mt-sm-30">
          <Link to={`/blog`} className="section-more">
            {t("blog.all_posts")} <i className="mi-chevron-right size-14" />
          </Link>
        </div>
      </div>
      <div className="row mt-n30">
        {/* Post Item */}
        {Array.isArray(blogPosts) &&
          blogPosts.map((post, index) => (
            <div
              key={post.id}
              className={`post-prev col-md-6 col-lg-4 mt-30 wow fadeInLeft`}
              data-wow-delay={`${index * 0.1}s`}
            >
              <div className="post-prev-container">
                <div className="post-prev-img">
                  <Link to={`/blog-single/${post.slug}`}>
                    <img
                      src={
                        post.featuredImage ||
                        "/assets/images/demo-elegant/blog/1.jpg"
                      }
                      width={607}
                      height={358}
                      alt={getTranslation(post, "title")}
                    />
                  </Link>
                </div>
                <h3 className="post-prev-title">
                  <Link to={`/blog-single/${post.slug}`}>
                    {getTranslation(post, "title")}
                  </Link>
                </h3>
                <div className="post-prev-text">
                  {getTranslation(post, "excerpt")}
                </div>
                <div className="post-prev-info clearfix">
                  <div className="float-start">
                    <a href="#" className="icon-author">
                      <i className="mi-user size-14 align-middle" />
                    </a>
                    <a href="#">{post.author?.name || "DevSkills Team"}</a>
                  </div>
                  <div className="float-end">
                    <i className="mi-calendar size-14 align-middle" />
                    <a href="#">
                      {new Date(
                        post.publishedAt || post.createdAt
                      ).toLocaleDateString()}
                    </a>
                  </div>
                </div>
              </div>
            </div>
          ))}
        {/* End Post Item */}
      </div>
    </div>
  );
}
