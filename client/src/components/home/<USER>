import AnimatedText from "@/components/common/AnimatedText";
import React from "react";
import { useTranslation } from "react-i18next";
import { trackButtonClick } from "@/utils/analytics";

export default function Hero() {
  const { t } = useTranslation();

  const handleDiscoverClick = () => {
    trackButtonClick("Discover Now", "Hero Section", {
      button_type: "cta",
      section: "hero",
    });
  };
  return (
    <div className="container min-height-100vh d-flex align-items-center pt-100 pb-100 pt-sm-120 pb-sm-120">
      {/* Home Section Content */}
      <div className="home-content text-center">
        <h2 className="section-title-tiny mb-50 mb-sm-30 wow fadeInDownShort">
          {t("hero.welcome")}
        </h2>
        <h1 className="hs-title-3 mb-120 mb-sm-80 mb-xs-140">
          <span className="wow charsAnimInLong" data-splitting="chars">
            <AnimatedText text={t("hero.studio")} />
          </span>
        </h1>
        <div className="local-scroll wow fadeInUpShort" data-wow-delay="0.57s">
          <a
            href="#about"
            className="link-hover-anim link-circle-1 align-middle"
            data-link-animate="y"
            onClick={handleDiscoverClick}
          >
            <span className="link-strong link-strong-unhovered">
              {t("hero.discover")}{" "}
              <i
                className="mi-arrow-right size-18 align-middle"
                aria-hidden="true"
              ></i>
            </span>
            <span
              className="link-strong link-strong-hovered"
              aria-hidden="true"
            >
              {t("hero.discover")}{" "}
              <i
                className="mi-arrow-right size-18 align-middle"
                aria-hidden="true"
              ></i>
            </span>
          </a>
        </div>
      </div>
      {/* End Home Section Content */}
      {/* Scroll Down */}
      <div
        className="local-scroll scroll-down-3-wrap wow fadeInUp"
        data-wow-offset={0}
      >
        <a href="#about" className="scroll-down-3">
          {t("hero.scrollDown") || "Scroll Down"}
        </a>
      </div>
      {/* End Scroll Down */}
    </div>
  );
}
