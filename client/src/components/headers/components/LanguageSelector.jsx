// client\src\components\headers\components\LanguageSelector.jsx

import React, { useState, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { languages } from "@/i18n";

export default function LanguageSelector() {
  const { i18n } = useTranslation();
  const currentLanguage = i18n.language || "et";

  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);
  const [isMobile, setIsMobile] = useState(
    typeof window !== "undefined" ? window.innerWidth <= 1024 : false
  );

  // Update isMobile state on window resize
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 1024);
    };

    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleLanguageChange = (langCode) => {
    i18n.changeLanguage(langCode);
    setIsOpen(false);

    // Track language change in analytics
    if (typeof window !== "undefined" && window.gtag) {
      window.gtag("event", "language_change", {
        event_category: "User Interaction",
        event_label: `${i18n.language} to ${langCode}`,
        from_language: i18n.language,
        to_language: langCode,
        send_to: "G-8NEGL4LL8Q",
      });
    }
  };

  // Mobile view - show languages side by side
  if (isMobile) {
    return (
      <div className="language-selector-mobile d-flex align-items-center">
        {Object.entries(languages).map(([langCode, langData]) => (
          <button
            key={langCode}
            className={`btn btn-link p-0 mx-2 ${
              langCode === currentLanguage
                ? "text-white"
                : "text-muted opacity-50"
            }`}
            onClick={() => handleLanguageChange(langCode)}
            aria-label={`Switch to ${langData.name}`}
          >
            <span className="language-code text-uppercase font-weight-bold">
              {langCode}
            </span>
          </button>
        ))}
      </div>
    );
  }

  // Desktop view - dropdown
  return (
    <div className="language-selector position-relative" ref={dropdownRef}>
      <button
        className="language-toggle btn btn-link p-0 d-flex align-items-center"
        onClick={toggleDropdown}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <span className="language-code text-uppercase">{currentLanguage}</span>
      </button>

      {isOpen && (
        <div className="language-dropdown position-absolute bg-dark-1 py-2 rounded shadow-sm">
          {Object.entries(languages).map(([langCode, langData]) => (
            <button
              key={langCode}
              className={`dropdown-item d-flex align-items-center px-3 py-1 ${
                langCode === currentLanguage ? "active" : ""
              }`}
              onClick={() => handleLanguageChange(langCode)}
            >
              <span className="me-2">{langData.flag}</span>
              <span className="language-name">{langData.name}</span>
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
