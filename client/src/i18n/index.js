import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import LanguageDetector from "i18next-browser-languagedetector";
import Backend from "i18next-http-backend";

// Language configuration
const languages = {
  en: { name: "English", flag: "🇬🇧" },
  et: { name: "<PERSON><PERSON><PERSON>", flag: "🇪🇪" },
  fi: { name: "<PERSON><PERSON>", flag: "🇫🇮" },
  de: { name: "<PERSON><PERSON><PERSON>", flag: "🇩🇪" },
  sv: { name: "<PERSON><PERSON>", flag: "🇸🇪" },
};

// Initialize i18next
i18n
  .use(Backend) // Load translations from files
  .use(LanguageDetector) // Detect user language
  .use(initReactI18next) // Pass i18n instance to react-i18next
  .init({
    // Language settings
    lng: "et", // Default language (Estonian as requested)
    fallbackLng: "en", // Fallback language
    supportedLngs: Object.keys(languages),

    // Namespace settings
    ns: ["translation"],
    defaultNS: "translation",

    // Backend settings for loading translations
    backend: {
      loadPath: "/locales/{{lng}}/{{ns}}.json",
    },

    // Language detection settings
    detection: {
      // Order of language detection methods
      order: [
        "path", // Check URL path first (/en/, /et/, etc.)
        "localStorage", // Then localStorage
        "navigator", // Then browser language
        "htmlTag", // Then HTML lang attribute
      ],

      // Cache user language preference
      caches: ["localStorage"],

      // localStorage key
      lookupLocalStorage: "i18nextLng",

      // Path detection settings
      lookupFromPathIndex: 0, // Language is the first segment: /en/about
      checkWhitelist: true, // Only allow supported languages

      // Don't lookup from path by default (we'll handle this manually)
      lookupFromPathIndex: 0,

      // Check all fallbacks
      checkWhitelist: true,
    },

    // Interpolation settings
    interpolation: {
      escapeValue: false, // React already escapes values
      formatSeparator: ",",
      format: (value, format, lng) => {
        if (format === "uppercase") return value.toUpperCase();
        if (format === "lowercase") return value.toLowerCase();
        if (format === "capitalize")
          return value.charAt(0).toUpperCase() + value.slice(1);
        return value;
      },
    },

    // React settings
    react: {
      useSuspense: false, // Disable suspense for SSR compatibility
      bindI18n: "languageChanged",
      bindI18nStore: "",
      transEmptyNodeValue: "",
      transSupportBasicHtmlNodes: true,
      transKeepBasicHtmlNodesFor: ["br", "strong", "i", "em", "span"],
    },

    // Debug settings (disable in production)
    debug: process.env.NODE_ENV === "development",

    // Load settings
    load: "languageOnly", // Load only language codes (en, not en-US)
    preload: Object.keys(languages), // Preload all supported languages

    // Cleanup settings
    cleanCode: true,

    // Key separator (use dots for nested keys)
    keySeparator: ".",
    nsSeparator: ":",

    // Pluralization
    pluralSeparator: "_",
    contextSeparator: "_",

    // Return objects for missing keys
    returnObjects: false,
    returnEmptyString: false,
    returnNull: false,

    // Join arrays
    joinArrays: false,

    // Post processing
    postProcess: false,

    // Save missing keys
    saveMissing: process.env.NODE_ENV === "development",
    saveMissingTo: "current",

    // Missing key handler
    missingKeyHandler: (lng, ns, key, fallbackValue) => {
      if (process.env.NODE_ENV === "development") {
        console.warn(`Missing translation key: ${key} for language: ${lng}`);
      }
    },

    // Update missing keys
    updateMissing: false,

    // Ignore JSON structure
    ignoreJSONStructure: true,
  });

// Export language configuration
export { languages };

// Export i18n instance
export default i18n;
