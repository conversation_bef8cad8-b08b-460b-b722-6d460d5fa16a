import WOW from "wow.js";
export function init_wow() {
  // Add error handling and better timing
  setTimeout(() => {
    try {
      /* Wow init */
      if (document.body.classList.contains("appear-animate")) {
        document
          .querySelectorAll(".wow")
          .forEach((el) => el.classList.add("no-animate"));
      }

      var wow = new WOW({
        boxClass: "wow",
        animateClass: "animated",
        offset: 100,
        mobile: true,
        live: false, // Disable live detection to prevent issues
        callback: function (box) {
          // Ensure element stays visible after animation
          box.classList.add("animated");
          box.style.opacity = "1";
          box.style.visibility = "visible";
        },
      });

      // Always ensure appear-animate class exists
      if (!document.body.classList.contains("appear-animate")) {
        document.body.classList.add("appear-animate");
      }

      // Initialize WOW
      wow.init();

      // Fallback: ensure all wow elements are visible
      setTimeout(() => {
        document.querySelectorAll(".wow").forEach((el) => {
          if (!el.classList.contains("animated")) {
            el.style.opacity = "1";
            el.style.visibility = "visible";
            el.classList.add("animated");
          }
        });
      }, 2000);

      /* Wow for portfolio init */
      if (document.body.classList.contains("appear-animate")) {
        document
          .querySelectorAll(".wow-p")
          .forEach((el) => el.classList.add("no-animate"));
      }
      var wow_p = new WOW({
        boxClass: "wow-p",
        animateClass: "animated",
        offset: 100,
        mobile: true,
        live: false,
        callback: function (box) {
          box.classList.add("animated");
          box.style.opacity = "1";
          box.style.visibility = "visible";
        },
      });

      if (document.body.classList.contains("appear-animate")) {
        wow_p.init();
      } else {
        document
          .querySelectorAll(".wow-p")
          .forEach((el) => (el.style.opacity = "1"));
      }

      /* Wow for menu bar init */
      if (
        document.body.classList.contains("appear-animate") &&
        window.innerWidth >= 1024 &&
        document.documentElement.classList.contains("no-mobile")
      ) {
        document.querySelectorAll(".wow-menubar").forEach((el) => {
          el.classList.add("no-animate", "fadeInDown", "animated");
          setInterval(() => {
            el.classList.remove("no-animate");
          }, 1500);
        });
      } else {
        document
          .querySelectorAll(".wow-menubar")
          .forEach((el) => (el.style.opacity = "1"));
      }
    } catch (error) {
      console.error("Error initializing WOW.js:", error);
      // Fallback: make all elements visible
      document.querySelectorAll(".wow, .wow-p, .wow-menubar").forEach((el) => {
        el.style.opacity = "1";
        el.classList.add("animated");
      });
    }
  }, 100);
}
