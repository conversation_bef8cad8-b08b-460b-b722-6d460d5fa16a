/**
 * Centralized syntax highlighting service using <PERSON><PERSON>
 * This service ensures only one instance of <PERSON><PERSON> is loaded and shared
 * between blog-single page and TipTapEditor
 */

let shikiInstance = null;
let shikiPromise = null;

/**
 * Initialize Shiki with common languages and themes
 * Returns a promise that resolves to the Shiki instance
 */
async function initializeShiki() {
  if (shikiInstance) {
    return shikiInstance;
  }

  if (shikiPromise) {
    return shikiPromise;
  }

  shikiPromise = (async () => {
    try {
      const { createHighlighter } = await import("shiki");

      // Initialize with common languages and a dark theme
      shikiInstance = await createHighlighter({
        themes: ["github-dark", "github-light"],
        langs: [
          "javascript",
          "typescript",
          "python",
          "java",
          "cpp",
          "css",
          "html",
          "json",
          "sql",
          "bash",
          "php",
          "go",
          "rust",
          "jsx",
          "tsx",
          "markdown",
          "yaml",
          "xml",
        ],
      });

      console.log("<PERSON><PERSON> initialized successfully");
      return shikiInstance;
    } catch (error) {
      console.error("Failed to initialize <PERSON><PERSON>:", error);
      throw error;
    }
  })();

  return shikiPromise;
}

/**
 * Highlight code blocks in the DOM using Shiki
 * @param {string} selector - CSS selector for code blocks (default: '.blog-content pre code')
 * @param {string} theme - Theme to use (default: 'github-dark')
 */
export async function highlightCodeBlocks(
  selector = ".blog-content pre code",
  theme = "github-dark"
) {
  try {
    const highlighter = await initializeShiki();
    const codeBlocks = document.querySelectorAll(selector);

    codeBlocks.forEach((block) => {
      const code = block.textContent || "";
      const language = detectLanguage(block) || "text";

      try {
        const highlighted = highlighter.codeToHtml(code, {
          lang: language,
          theme: theme,
        });

        // Extract just the inner HTML (without the outer <pre> wrapper)
        const tempDiv = document.createElement("div");
        tempDiv.innerHTML = highlighted;
        const preElement = tempDiv.querySelector("pre");

        if (preElement) {
          block.innerHTML = preElement.innerHTML;
          block.classList.add("shiki-highlighted");
        }
      } catch (langError) {
        console.warn(
          `Failed to highlight code block with language "${language}":`,
          langError
        );
        // Fallback: just add basic styling without syntax highlighting
        block.classList.add("shiki-fallback");
      }
    });
  } catch (error) {
    console.error("Failed to highlight code blocks:", error);
    // Graceful degradation - code blocks will still be visible
  }
}

/**
 * Get Shiki instance for custom usage (e.g., in TipTapEditor)
 * @returns {Promise<Object>} Shiki highlighter instance
 */
export async function getShikiInstance() {
  return initializeShiki();
}

/**
 * Detect language from code block class or parent element
 * @param {Element} codeElement - The code element
 * @returns {string|null} Detected language or null
 */
function detectLanguage(codeElement) {
  // Check for language class on code element
  const codeClasses = codeElement.className.match(/language-(\w+)/);
  if (codeClasses) {
    return codeClasses[1];
  }

  // Check for language class on parent pre element
  const preElement = codeElement.closest("pre");
  if (preElement) {
    const preClasses = preElement.className.match(/language-(\w+)/);
    if (preClasses) {
      return preClasses[1];
    }
  }

  // Try to detect from content (basic heuristics)
  const code = codeElement.textContent || "";

  if (code.includes("function") && code.includes("{")) {
    return "javascript";
  }
  if (code.includes("def ") && code.includes(":")) {
    return "python";
  }
  if (code.includes("<?php")) {
    return "php";
  }
  if (code.includes("<html") || code.includes("<!DOCTYPE")) {
    return "html";
  }
  if (code.includes("SELECT") || code.includes("FROM")) {
    return "sql";
  }

  return "text";
}

/**
 * Create a Shiki-compatible lowlight instance for TipTap
 * This provides compatibility with TipTap's CodeBlockLowlight extension
 */
export async function createShikiLowlight() {
  try {
    const highlighter = await initializeShiki();

    // Create a lowlight-compatible interface that matches the expected API
    const lowlight = {
      highlight: (code, options = {}) => {
        try {
          // Handle both string and object parameters
          const language =
            typeof options === "string" ? options : options.language || "text";

          const highlighted = highlighter.codeToHtml(code, {
            lang: language,
            theme: "github-dark",
          });

          // Parse the HTML to create a proper AST-like structure
          const tempDiv = document.createElement("div");
          tempDiv.innerHTML = highlighted;
          const preElement = tempDiv.querySelector("pre");

          if (preElement) {
            // Create a simplified AST structure that TipTap can understand
            const children = Array.from(preElement.childNodes)
              .map((node) => {
                if (node.nodeType === Node.TEXT_NODE) {
                  return {
                    type: "text",
                    value: node.textContent,
                  };
                } else if (node.nodeType === Node.ELEMENT_NODE) {
                  return {
                    type: "element",
                    tagName: node.tagName.toLowerCase(),
                    properties: {
                      className: node.className
                        ? node.className.split(" ")
                        : [],
                    },
                    children: [
                      {
                        type: "text",
                        value: node.textContent,
                      },
                    ],
                  };
                }
                return null;
              })
              .filter(Boolean);

            return {
              type: "root",
              children: [
                {
                  type: "element",
                  tagName: "pre",
                  properties: {
                    className: preElement.className
                      ? preElement.className.split(" ")
                      : [],
                  },
                  children: children,
                },
              ],
              data: {
                language: language,
              },
            };
          }
        } catch (error) {
          console.warn(
            `Failed to highlight with language "${language}":`,
            error
          );
        }

        // Fallback - return simple structure
        return {
          type: "root",
          children: [
            {
              type: "element",
              tagName: "pre",
              properties: {},
              children: [
                {
                  type: "text",
                  value: code,
                },
              ],
            },
          ],
          data: {
            language:
              typeof options === "string"
                ? options
                : options.language || "text",
          },
        };
      },

      highlightAuto: (code) => {
        const detectedLang = detectLanguageFromCode(code);
        return lowlight.highlight(code, { language: detectedLang });
      },

      // Add register method for compatibility (even though we don't use it)
      register: () => {
        // No-op since Shiki handles language registration internally
      },

      // Add registered method for compatibility
      registered: (language) => {
        // Return true for common languages, false for others
        const supportedLangs = [
          "javascript",
          "typescript",
          "python",
          "java",
          "cpp",
          "css",
          "html",
          "json",
          "sql",
          "bash",
          "php",
          "go",
          "rust",
          "jsx",
          "tsx",
        ];
        return supportedLangs.includes(language);
      },
    };

    return lowlight;
  } catch (error) {
    console.error("Failed to create Shiki lowlight:", error);
    throw error;
  }
}

/**
 * Detect language from code content using heuristics
 * @param {string} code - Code content
 * @returns {string} Detected language
 */
function detectLanguageFromCode(code) {
  if (code.includes("function") && code.includes("{")) {
    return "javascript";
  }
  if (code.includes("def ") && code.includes(":")) {
    return "python";
  }
  if (code.includes("<?php")) {
    return "php";
  }
  if (code.includes("<html") || code.includes("<!DOCTYPE")) {
    return "html";
  }
  if (code.includes("SELECT") || code.includes("FROM")) {
    return "sql";
  }
  if (code.includes("import ") && code.includes("from ")) {
    return "python";
  }
  if (code.includes("import React") || code.includes("export default")) {
    return "jsx";
  }

  return "text";
}
