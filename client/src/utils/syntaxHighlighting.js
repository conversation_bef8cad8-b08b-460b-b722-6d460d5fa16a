/**
 * Centralized syntax highlighting service using Lowlight
 * This service ensures only one instance of Lowlight is loaded and shared
 * between blog-single page and TipTapEditor
 */

let lowlightInstance = null;
let lowlightPromise = null;

/**
 * Initialize Lowlight with common languages
 * Returns a promise that resolves to the Lowlight instance
 */
async function initializeLowlight() {
  if (lowlightInstance) {
    return lowlightInstance;
  }

  if (lowlightPromise) {
    return lowlightPromise;
  }

  lowlightPromise = (async () => {
    try {
      const { createLowlight, common } = await import("lowlight");

      // Initialize with common languages (includes JS, TS, Python, CSS, HTML, JSON, etc.)
      lowlightInstance = createLowlight(common);

      console.log("Lowlight initialized successfully");
      return lowlightInstance;
    } catch (error) {
      console.error("Failed to initialize Lowlight:", error);
      throw error;
    }
  })();

  return lowlightPromise;
}

/**
 * Highlight code blocks in the DOM using Lowlight
 * @param {string} selector - CSS selector for code blocks (default: '.blog-content pre code')
 */
export async function highlightCodeBlocks(selector = ".blog-content pre code") {
  try {
    const lowlight = await initializeLowlight();
    const codeBlocks = document.querySelectorAll(selector);

    codeBlocks.forEach((block) => {
      const code = block.textContent || "";
      const language = detectLanguage(block) || "text";

      try {
        // Use lowlight to highlight the code
        const result = lowlight.highlight(language, code);

        // Convert lowlight AST to HTML
        const html = astToHtml(result);

        if (html) {
          block.innerHTML = html;
          block.classList.add("lowlight-highlighted");
        }
      } catch (langError) {
        console.warn(
          `Failed to highlight code block with language "${language}":`,
          langError
        );
        // Fallback: just add basic styling without syntax highlighting
        block.classList.add("lowlight-fallback");
      }
    });
  } catch (error) {
    console.error("Failed to highlight code blocks:", error);
    // Graceful degradation - code blocks will still be visible
  }
}

/**
 * Get Lowlight instance for custom usage (e.g., in TipTapEditor)
 * @returns {Promise<Object>} Lowlight highlighter instance
 */
export async function getLowlightInstance() {
  return initializeLowlight();
}

/**
 * Detect language from code block class or parent element
 * @param {Element} codeElement - The code element
 * @returns {string|null} Detected language or null
 */
function detectLanguage(codeElement) {
  // Check for language class on code element
  const codeClasses = codeElement.className.match(/language-(\w+)/);
  if (codeClasses) {
    return codeClasses[1];
  }

  // Check for language class on parent pre element
  const preElement = codeElement.closest("pre");
  if (preElement) {
    const preClasses = preElement.className.match(/language-(\w+)/);
    if (preClasses) {
      return preClasses[1];
    }
  }

  // Try to detect from content (basic heuristics)
  const code = codeElement.textContent || "";

  if (code.includes("function") && code.includes("{")) {
    return "javascript";
  }
  if (code.includes("def ") && code.includes(":")) {
    return "python";
  }
  if (code.includes("<?php")) {
    return "php";
  }
  if (code.includes("<html") || code.includes("<!DOCTYPE")) {
    return "html";
  }
  if (code.includes("SELECT") || code.includes("FROM")) {
    return "sql";
  }

  return "text";
}

/**
 * Convert lowlight AST to HTML string
 * @param {Object} ast - Lowlight AST result
 * @returns {string} HTML string
 */
function astToHtml(ast) {
  if (!ast || !ast.children) return "";

  return ast.children
    .map((node) => {
      if (node.type === "text") {
        return node.value;
      } else if (node.type === "element") {
        const className = node.properties?.className?.join(" ") || "";
        const classAttr = className ? ` class="${className}"` : "";
        const children = node.children
          ? node.children
              .map((child) => astToHtml({ children: [child] }))
              .join("")
          : "";
        return `<${node.tagName}${classAttr}>${children}</${node.tagName}>`;
      }
      return "";
    })
    .join("");
}
