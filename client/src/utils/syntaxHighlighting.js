/**
 * Centralized syntax highlighting service using Prism.js
 * This service ensures only essential languages are loaded and shared
 * between blog-single page and TipTapEditor
 */

let prismInstance = null;
let prismPromise = null;

/**
 * Initialize Prism.js with essential languages only
 * Returns a promise that resolves to the Prism instance
 */
async function initializePrism() {
  if (prismInstance) {
    return prismInstance;
  }

  if (prismPromise) {
    return prismPromise;
  }

  prismPromise = (async () => {
    try {
      // Import Prism core
      const Prism = (await import("prismjs")).default;

      // Import Prism CSS theme (dark theme for better visual appeal)
      await import("prismjs/themes/prism-tomorrow.css");

      // Import only essential languages (saves massive bundle size)
      await import("prismjs/components/prism-javascript");
      await import("prismjs/components/prism-typescript");
      await import("prismjs/components/prism-css");
      await import("prismjs/components/prism-python");
      await import("prismjs/components/prism-json");
      await import("prismjs/components/prism-bash");
      await import("prismjs/components/prism-sql");

      prismInstance = Prism;
      console.log("Prism.js initialized successfully with essential languages");
      return prismInstance;
    } catch (error) {
      console.error("Failed to initialize Prism.js:", error);
      throw error;
    }
  })();

  return prismPromise;
}

/**
 * Highlight code blocks in the DOM using Prism.js
 * @param {string} selector - CSS selector for code blocks (default: '.blog-content pre code')
 */
export async function highlightCodeBlocks(selector = ".blog-content pre code") {
  try {
    const Prism = await initializePrism();
    const codeBlocks = document.querySelectorAll(selector);

    codeBlocks.forEach((block) => {
      const code = block.textContent || "";
      const language = detectLanguage(block) || "javascript";

      try {
        // Use Prism to highlight the code
        const grammar = Prism.languages[language];

        if (grammar) {
          const html = Prism.highlight(code, grammar, language);
          block.innerHTML = html;
          block.classList.add("prism-highlighted");
          block.classList.add(`language-${language}`);
        } else {
          // Fallback: just add basic styling without syntax highlighting
          block.classList.add("prism-fallback");
          console.warn(`Language "${language}" not supported by Prism.js`);
        }
      } catch (langError) {
        console.warn(
          `Failed to highlight code block with language "${language}":`,
          langError
        );
        // Fallback: just add basic styling without syntax highlighting
        block.classList.add("prism-fallback");
      }
    });
  } catch (error) {
    console.error("Failed to highlight code blocks:", error);
    // Graceful degradation - code blocks will still be visible
  }
}

/**
 * Get Prism instance for custom usage (e.g., in TipTapEditor)
 * @returns {Promise<Object>} Prism highlighter instance
 */
export async function getPrismInstance() {
  return initializePrism();
}

/**
 * Detect language from code block class or parent element
 * @param {Element} codeElement - The code element
 * @returns {string|null} Detected language or null
 */
function detectLanguage(codeElement) {
  // Check for language class on code element
  const codeClasses = codeElement.className.match(/language-(\w+)/);
  if (codeClasses) {
    return codeClasses[1];
  }

  // Check for language class on parent pre element
  const preElement = codeElement.closest("pre");
  if (preElement) {
    const preClasses = preElement.className.match(/language-(\w+)/);
    if (preClasses) {
      return preClasses[1];
    }
  }

  // Try to detect from content (basic heuristics)
  const code = codeElement.textContent || "";

  if (code.includes("function") && code.includes("{")) {
    return "javascript";
  }
  if (code.includes("def ") && code.includes(":")) {
    return "python";
  }
  if (code.includes("<?php")) {
    return "php";
  }
  if (code.includes("<html") || code.includes("<!DOCTYPE")) {
    return "html";
  }
  if (code.includes("SELECT") || code.includes("FROM")) {
    return "sql";
  }

  return "text";
}

// Removed old Lowlight-specific helper functions
// Prism.js handles HTML generation internally
