export const headerChangeOnScroll = () => {
  var mainNav = document.querySelector(".main-nav");
  var navLogoWrapLogo = document.querySelector(".nav-logo-wrap .logo");
  var lightAfterScroll = document.querySelector(".light-after-scroll");

  // Exit early if main navigation doesn't exist (e.g., on admin pages)
  if (!mainNav) {
    return;
  }

  // Check if we're on a page that should maintain dark navbar
  const shouldStayDark =
    mainNav.classList.contains("dark-mode") ||
    window.location.pathname === "/" ||
    window.location.pathname.match(/^\/[a-z]{2}\/?$/); // matches /en/, /et/, etc.

  if (window.scrollY > 0) {
    mainNav.classList.remove("transparent");
    mainNav.classList.add("small-height", "body-scrolled");
    if (navLogoWrapLogo) navLogoWrapLogo.classList.add("small-height");

    // Only remove dark class if we're not on a page that should stay dark
    if (lightAfterScroll && !shouldStayDark) {
      lightAfterScroll.classList.remove("dark");
    }

    // Ensure dark navbar stays dark on scroll
    if (shouldStayDark && !mainNav.classList.contains("dark")) {
      mainNav.classList.add("dark");
    }
  } else if (window.scrollY === 0) {
    mainNav.classList.add("transparent");
    mainNav.classList.remove("small-height", "body-scrolled");
    if (navLogoWrapLogo) navLogoWrapLogo.classList.remove("small-height");

    // Only add dark class back if we're not on a page that should stay dark
    if (lightAfterScroll && !shouldStayDark) {
      lightAfterScroll.classList.add("dark");
    }

    // Ensure dark navbar stays dark when back to top
    if (shouldStayDark && !mainNav.classList.contains("dark")) {
      mainNav.classList.add("dark");
    }
  }
};
