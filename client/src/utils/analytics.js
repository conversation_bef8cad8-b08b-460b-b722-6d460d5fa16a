// Google Analytics 4 utility functions

// Extract language from URL path
const extractLanguageFromPath = (path) => {
  const match = path.match(/\/([a-z]{2})\//);
  return match ? match[1] : "en";
};

// Get page type from URL
const getPageType = (path) => {
  if (path.includes("/blog-single/")) return "blog_post";
  if (path.includes("/blog")) return "blog_list";
  if (path.includes("/products/")) return "product";
  if (path.includes("/about")) return "about";
  if (path.includes("/contact")) return "contact";
  if (path.includes("/services")) return "services";
  if (path.includes("/portfolio")) return "portfolio";
  return "home";
};

// Track custom events to our backend
// Get API base URL - same pattern as api.jsx
const getApiBaseUrl = () => {
  // In production, both frontend and backend are served from the same domain
  if (import.meta.env.PROD) {
    return `${window.location.protocol}//${window.location.host}`;
  }

  // In development, backend runs on port 4004
  return "http://localhost:4004";
};

export const trackCustomEvent = async (
  eventName,
  eventCategory,
  eventLabel,
  eventValue = null,
  metadata = {}
) => {
  try {
    const language = extractLanguageFromPath(window.location.pathname);
    const path = window.location.pathname;

    // Extract blog post slug if on a blog post page
    let blogPostId = null;
    const blogPostMatch = path.match(/\/blog-single\/([^\/]+)/);
    if (blogPostMatch) {
      const slug = blogPostMatch[1];
      // The backend will resolve the slug to blogPostId
      blogPostId = slug;
    }

    const apiBaseUrl = getApiBaseUrl();
    await fetch(`${apiBaseUrl}/api/analytics/event`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        eventName,
        eventCategory,
        eventLabel,
        eventValue,
        path,
        language,
        blogPostId,
        metadata: {
          ...metadata,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          referrer: document.referrer,
        },
      }),
    });
  } catch (error) {
    console.error("Error tracking custom event:", error);
  }
};

// Track page views (non-blocking)
export const trackPageView = (page_title, page_location) => {
  // Use requestIdleCallback for better performance, fallback to setTimeout
  const trackFn = () => {
    if (typeof window !== "undefined" && window.gtag) {
      const language = extractLanguageFromPath(page_location);

      window.gtag("event", "page_view", {
        page_title: page_title,
        page_location: page_location,
        language: language,
        page_type: getPageType(page_location),
        send_to: "G-8NEGL4LL8Q",
      });

      // Also track in our custom analytics
      trackCustomEvent("page_view", "navigation", page_title, null, {
        language: language,
        page_type: getPageType(page_location),
      });
    }
  };

  if (typeof window !== "undefined" && window.requestIdleCallback) {
    window.requestIdleCallback(trackFn);
  } else {
    setTimeout(trackFn, 0);
  }
};

// Track button clicks
export const trackButtonClick = (
  button_name,
  button_location,
  additional_params = {}
) => {
  if (typeof window !== "undefined" && window.gtag) {
    const language = extractLanguageFromPath(window.location.pathname);

    window.gtag("event", "click", {
      event_category: "Button",
      event_label: button_name,
      button_location: button_location,
      language: language,
      page_type: getPageType(window.location.pathname),
      ...additional_params,
      send_to: "G-8NEGL4LL8Q",
    });

    // Also track in our custom analytics
    trackCustomEvent("click", "Button", button_name, null, {
      button_location,
      language,
      page_type: getPageType(window.location.pathname),
      ...additional_params,
    });
  }
};

// Track form submissions
export const trackFormSubmission = (
  form_name,
  form_location,
  success = true
) => {
  if (typeof window !== "undefined" && window.gtag) {
    window.gtag("event", success ? "form_submit" : "form_error", {
      event_category: "Form",
      event_label: form_name,
      form_location: form_location,
      success: success,
      send_to: "G-8NEGL4LL8Q",
    });
  }
};

// Track contact form submissions (conversion)
export const trackContactFormSubmission = (email, message_length) => {
  if (typeof window !== "undefined" && window.gtag) {
    // Track as conversion
    window.gtag("event", "conversion", {
      send_to: "G-8NEGL4LL8Q",
      event_category: "Contact",
      event_label: "Contact Form Submission",
      value: 1,
      currency: "EUR",
      user_email: email,
      message_length: message_length,
    });

    // Also track as generate_lead
    window.gtag("event", "generate_lead", {
      send_to: "G-8NEGL4LL8Q",
      event_category: "Lead Generation",
      event_label: "Contact Form",
      value: 1,
      currency: "EUR",
    });
  }
};

// Track language changes
export const trackLanguageChange = (from_language, to_language) => {
  if (typeof window !== "undefined" && window.gtag) {
    window.gtag("event", "language_change", {
      event_category: "User Interaction",
      event_label: `${from_language} to ${to_language}`,
      from_language: from_language,
      to_language: to_language,
      send_to: "G-8NEGL4LL8Q",
    });
  }
};

// Track navigation menu interactions
export const trackNavigation = (menu_item, menu_location) => {
  if (typeof window !== "undefined" && window.gtag) {
    window.gtag("event", "navigation_click", {
      event_category: "Navigation",
      event_label: menu_item,
      menu_location: menu_location,
      send_to: "G-8NEGL4LL8Q",
    });
  }
};

// Track scroll depth
export const trackScrollDepth = (scroll_percentage, page_location) => {
  if (typeof window !== "undefined" && window.gtag) {
    window.gtag("event", "scroll", {
      event_category: "User Engagement",
      event_label: `${scroll_percentage}% scrolled`,
      scroll_percentage: scroll_percentage,
      page_location: page_location,
      send_to: "G-8NEGL4LL8Q",
    });
  }
};

// Track time on page
export const trackTimeOnPage = (time_seconds, page_location) => {
  if (typeof window !== "undefined" && window.gtag) {
    window.gtag("event", "timing_complete", {
      name: "page_view_time",
      value: time_seconds,
      event_category: "User Engagement",
      event_label: page_location,
      send_to: "G-8NEGL4LL8Q",
    });

    // Also track in our custom analytics
    trackCustomEvent(
      "timing_complete",
      "User Engagement",
      page_location,
      time_seconds,
      {
        page_view_time: time_seconds,
      }
    );
  }
};

// Track file downloads
export const trackFileDownload = (file_name, file_type, download_location) => {
  if (typeof window !== "undefined" && window.gtag) {
    window.gtag("event", "file_download", {
      event_category: "Downloads",
      event_label: file_name,
      file_type: file_type,
      download_location: download_location,
      send_to: "G-8NEGL4LL8Q",
    });
  }
};

// Track external link clicks
export const trackExternalLink = (link_url, link_text, link_location) => {
  if (typeof window !== "undefined" && window.gtag) {
    window.gtag("event", "click", {
      event_category: "External Link",
      event_label: link_text,
      link_url: link_url,
      link_location: link_location,
      send_to: "G-8NEGL4LL8Q",
    });
  }
};

// Track search queries (if you have search functionality)
export const trackSearch = (search_term, search_results_count) => {
  if (typeof window !== "undefined" && window.gtag) {
    window.gtag("event", "search", {
      search_term: search_term,
      event_category: "Search",
      event_label: search_term,
      search_results: search_results_count,
      send_to: "G-8NEGL4LL8Q",
    });
  }
};

// Track Business Comanager conversion events
export const trackComanagerConversion = (
  source_location,
  additional_params = {}
) => {
  if (typeof window !== "undefined" && window.gtag) {
    const language = extractLanguageFromPath(window.location.pathname);

    // Track as conversion event
    window.gtag("event", "comanager_conversion", {
      event_category: "Conversion",
      event_label: "Business Comanager CTA",
      source_location: source_location,
      language: language,
      page_type: getPageType(window.location.pathname),
      value: 100, // Assign conversion value
      currency: "EUR",
      ...additional_params,
      send_to: "G-8NEGL4LL8Q",
    });

    // Also track as purchase event for GA4 conversion tracking
    window.gtag("event", "purchase", {
      transaction_id: `comanager_${Date.now()}`,
      value: 100,
      currency: "EUR",
      items: [
        {
          item_id: "comanager_cta",
          item_name: "Business Comanager CTA Click",
          item_category: "Conversion",
          item_variant: source_location,
          quantity: 1,
          price: 100,
        },
      ],
      source_location: source_location,
      language: language,
      send_to: "G-8NEGL4LL8Q",
    });

    // Track in our custom analytics
    trackCustomEvent(
      "comanager_conversion",
      "Conversion",
      source_location,
      100,
      {
        language,
        page_type: getPageType(window.location.pathname),
        conversion_type: "comanager_cta",
        ...additional_params,
      }
    );
  }
};

// Track video interactions
export const trackVideoInteraction = (
  video_title,
  action,
  video_progress = null
) => {
  if (typeof window !== "undefined" && window.gtag) {
    const eventData = {
      event_category: "Video",
      event_label: video_title,
      video_action: action,
      send_to: "G-8NEGL4LL8Q",
    };

    if (video_progress !== null) {
      eventData.video_progress = video_progress;
    }

    window.gtag("event", `video_${action}`, eventData);
  }
};

// Track user engagement milestones
export const trackEngagementMilestone = (
  milestone_name,
  page_location,
  additional_data = {}
) => {
  if (typeof window !== "undefined" && window.gtag) {
    window.gtag("event", "engagement_milestone", {
      event_category: "User Engagement",
      event_label: milestone_name,
      page_location: page_location,
      ...additional_data,
      send_to: "G-8NEGL4LL8Q",
    });
  }
};

// Generic track event function for webstore and other components
export const trackEvent = (eventName, eventData = {}) => {
  if (typeof window !== "undefined" && window.gtag) {
    const language = extractLanguageFromPath(window.location.pathname);

    window.gtag("event", eventName, {
      event_category: eventData.category || "User Interaction",
      event_label: eventData.label || eventName,
      language: language,
      page_type: getPageType(window.location.pathname),
      ...eventData,
      send_to: "G-8NEGL4LL8Q",
    });

    // Also track in our custom analytics
    trackCustomEvent(
      eventName,
      eventData.category || "User Interaction",
      eventData.label || eventName,
      eventData.value || null,
      {
        language,
        page_type: getPageType(window.location.pathname),
        ...eventData,
      }
    );
  }
};

// Debug function to check if analytics is working
export const debugAnalytics = () => {
  if (typeof window !== "undefined") {
    console.log("Google Analytics Debug Info:");
    console.log("gtag available:", typeof window.gtag !== "undefined");
    console.log("dataLayer:", window.dataLayer);

    if (window.gtag) {
      // Test event
      window.gtag("event", "debug_test", {
        event_category: "Debug",
        event_label: "Analytics Test",
        send_to: "G-8NEGL4LL8Q",
      });
      console.log("Test event sent");
    }
  }
};
