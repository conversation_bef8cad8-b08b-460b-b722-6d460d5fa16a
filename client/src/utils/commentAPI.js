import { apiCall } from "./api";

// Get comments for admin
export const getAdminComments = async (params = {}) => {
  try {
    const queryParams = new URLSearchParams();

    if (params.page) queryParams.append("page", params.page);
    if (params.limit) queryParams.append("limit", params.limit);
    if (params.status) queryParams.append("status", params.status);
    if (params.search) queryParams.append("search", params.search);
    if (params.blogPostId) queryParams.append("blogPostId", params.blogPostId);

    const { response, data } = await apiCall(`/admin/comments?${queryParams}`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error("Get admin comments error:", error);
    throw error;
  }
};

// Approve comment
export const approveComment = async (commentId) => {
  try {
    const { response, data } = await apiCall(
      `/admin/comments/${commentId}/approve`,
      {
        method: "PATCH",
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error("Approve comment error:", error);
    throw error;
  }
};

// Reject/Hide comment
export const rejectComment = async (commentId) => {
  try {
    const { response, data } = await apiCall(
      `/admin/comments/${commentId}/reject`,
      {
        method: "PATCH",
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error("Reject comment error:", error);
    throw error;
  }
};

// Delete comment
export const deleteComment = async (commentId) => {
  try {
    const { response, data } = await apiCall(`/admin/comments/${commentId}`, {
      method: "DELETE",
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error("Delete comment error:", error);
    throw error;
  }
};

// Get comment statistics
export const getCommentStats = async () => {
  try {
    const { response, data: result } = await apiCall("/admin/comments?limit=1");

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // Get counts for different statuses
    const [pendingResult, approvedResult] = await Promise.all([
      apiCall("/admin/comments?status=pending&limit=1"),
      apiCall("/admin/comments?status=approved&limit=1"),
    ]);

    return {
      total: result.data.pagination.total,
      pending: pendingResult.data.data.pagination.total,
      approved: approvedResult.data.data.pagination.total,
    };
  } catch (error) {
    console.error("Get comment stats error:", error);
    throw error;
  }
};
