// client\src\store\languageStore.js

import { create } from "zustand";
import enTranslations from "../translations/en";
import etTranslations from "../translations/et";

// Available languages
const languages = {
  en: {
    code: "en",
    name: "English",
    translations: enTranslations,
    flag: "🇬🇧",
  },
  et: {
    code: "et",
    name: "Estonian",
    translations: etTranslations,
    flag: "🇪🇪",
  },
};

// Function to detect browser language and determine initial language
const getInitialLanguage = () => {
  try {
    // First, check if user has manually selected a language (stored in localStorage)
    if (typeof window !== "undefined" && window.localStorage) {
      const savedLanguage = localStorage.getItem("language");
      if (savedLanguage && languages[savedLanguage]) {
        return savedLanguage;
      }
    }

    // If no saved preference, detect browser language
    if (typeof navigator !== "undefined") {
      const browserLanguage = navigator.language || navigator.userLanguage;
      const browserLangCode = browserLanguage.split("-")[0].toLowerCase();

      // Check if browser language is supported
      if (languages[browserLangCode]) {
        return browserLangCode;
      }
    }
  } catch (error) {
    console.warn("Error detecting language:", error);
  }

  // Fallback to Estonian as requested
  return "et";
};

// Create the language store
export const useLanguageStore = create((set, get) => ({
  // Get initial language with browser detection and localStorage preference
  currentLanguage: getInitialLanguage(),

  // Function to change the language
  changeLanguage: (langCode) => {
    if (languages[langCode]) {
      try {
        const previousLanguage = get().currentLanguage;

        if (typeof window !== "undefined" && window.localStorage) {
          localStorage.setItem("language", langCode);
        }
        set({ currentLanguage: langCode });
        console.log("Language changed to:", langCode);

        // Track language change in analytics
        if (
          typeof window !== "undefined" &&
          window.gtag &&
          previousLanguage !== langCode
        ) {
          window.gtag("event", "language_change", {
            event_category: "User Interaction",
            event_label: `${previousLanguage} to ${langCode}`,
            from_language: previousLanguage,
            to_language: langCode,
            send_to: "G-8NEGL4LL8Q",
          });
        }
      } catch (error) {
        console.warn("Error saving language preference:", error);
        // Still update the state even if localStorage fails
        set({ currentLanguage: langCode });
      }
    }
  },

  // Get all available languages
  getLanguages: () => languages,
}));

// Custom hook for reactive translations
export const useTranslation = () => {
  const currentLanguage = useLanguageStore((state) => state.currentLanguage);

  const t = (key) => {
    const translations = languages[currentLanguage].translations;
    return translations[key] || key; // Return the key if translation not found
  };

  return { t, currentLanguage };
};
