// Testimonial slider with draggable functionality
document.addEventListener("DOMContentLoaded", function () {
  const slider = document.getElementById("testimonial-marquee");
  const track = document.getElementById("testimonial-track");

  if (!slider || !track) return;

  let isDown = false;
  let startX;
  let walkX = 0;
  let animationPaused = false;

  // Save the original animation
  const originalAnimation = getComputedStyle(track).animation;

  // Function to pause animation and store current position
  function pauseAnimation() {
    if (!animationPaused) {
      // Get current computed transform
      const style = window.getComputedStyle(track);
      const matrix = new WebKitCSSMatrix(style.transform);
      // Set the transform directly to keep current position
      track.style.animation = "none";
      track.style.transform = `translateX(${matrix.m41}px)`;
      animationPaused = true;
    }
  }

  // Function to resume animation
  function resumeAnimation() {
    if (animationPaused) {
      // Reset to original animation
      setTimeout(() => {
        track.style.animation = originalAnimation;
        track.style.transform = "";
        animationPaused = false;
      }, 50);
    }
  }

  // Mouse events
  slider.addEventListener("mousedown", (e) => {
    pauseAnimation();
    isDown = true;
    slider.classList.add("active");
    startX = e.pageX;
    walkX = 0;
    e.preventDefault();
  });

  slider.addEventListener("mouseleave", () => {
    if (isDown) {
      isDown = false;
      slider.classList.remove("active");
      resumeAnimation();
    }
  });

  slider.addEventListener("mouseup", () => {
    isDown = false;
    slider.classList.remove("active");
    resumeAnimation();
  });

  slider.addEventListener("mousemove", (e) => {
    if (!isDown) return;
    e.preventDefault();
    const x = e.pageX;
    const walk = x - startX;
    walkX += walk;
    startX = x;

    // Get current transform and add the walk distance
    const style = window.getComputedStyle(track);
    const matrix = new WebKitCSSMatrix(style.transform);
    track.style.transform = `translateX(${matrix.m41 + walk}px)`;
  });

  // Touch events for mobile
  slider.addEventListener(
    "touchstart",
    (e) => {
      pauseAnimation();
      isDown = true;
      slider.classList.add("active");
      startX = e.touches[0].pageX;
      walkX = 0;
      e.preventDefault();
    },
    { passive: false }
  );

  slider.addEventListener("touchend", () => {
    isDown = false;
    slider.classList.remove("active");
    resumeAnimation();
  });

  slider.addEventListener(
    "touchmove",
    (e) => {
      if (!isDown) return;
      e.preventDefault();
      const x = e.touches[0].pageX;
      const walk = x - startX;
      walkX += walk;
      startX = x;

      // Get current transform and add the walk distance
      const style = window.getComputedStyle(track);
      const matrix = new WebKitCSSMatrix(style.transform);
      track.style.transform = `translateX(${matrix.m41 + walk}px)`;
    },
    { passive: false }
  );

  // Pause animation on hover
  slider.addEventListener("mouseenter", () => {
    pauseAnimation();
  });

  // Resume animation when mouse leaves
  slider.addEventListener("mouseleave", () => {
    if (!isDown) {
      resumeAnimation();
    }
  });
});
