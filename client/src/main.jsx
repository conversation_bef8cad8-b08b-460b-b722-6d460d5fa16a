import React from "react";
import { createRoot, hydrateRoot } from "react-dom/client";
import App from "./App.jsx";
import { BrowserRouter } from "react-router-dom";
import { HelmetProvider } from "react-helmet-async";
import ErrorBoundary from "./components/common/ErrorBoundary.jsx";
import "./styles/languageSelector.css";
import "./styles/tiptap.css";

// Ready for Google Analytics and Google Tag Manager implementation

// For react-snap compatibility
const rootElement = document.getElementById("root");

// Define the app content once to avoid duplication
const AppWithProviders = (
  <React.StrictMode>
    <ErrorBoundary>
      <HelmetProvider>
        <BrowserRouter
          future={{
            v7_startTransition: true,
            v7_relativeSplatPath: true,
          }}
        >
          <App />
        </BrowserRouter>
      </HelmetProvider>
    </ErrorBoundary>
  </React.StrictMode>
);

// Check if the document has been prerendered by react-snap
const hasPrerendered = rootElement && rootElement.hasChildNodes();

// Use the appropriate rendering method
try {
  if (hasPrerendered) {
    // For prerendered content, use React 18 hydrateRoot
    hydrateRoot(rootElement, AppWithProviders);
  } else {
    // For fresh loads, use standard rendering
    const root = createRoot(rootElement);
    root.render(AppWithProviders);
  }
} catch (error) {
  console.error("Error rendering app:", error);
  // Fallback rendering
  const root = createRoot(rootElement);
  root.render(AppWithProviders);
}
