// client/src/pages/AdminBlogAnalytics.jsx

import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import SEO from "../components/common/SEO";
import AdminLayout from "../components/admin/AdminLayout";
import TimeRangeSelector from "../components/analytics/TimeRangeSelector";
import LanguageSelector from "../components/analytics/LanguageSelector";
import AnalyticsOverview from "../components/analytics/AnalyticsOverview";
import AnalyticsChart from "../components/analytics/AnalyticsChart";
import HeatmapChart from "../components/analytics/HeatmapChart";
import PostsTable from "../components/analytics/PostsTable";
import ConversionAnalytics from "../components/analytics/ConversionAnalytics";
import StaticPagesAnalytics from "../components/analytics/StaticPagesAnalytics";
import { adminAPI } from "../utils/api";

const AdminBlogAnalytics = () => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [timeRange, setTimeRange] = useState("last30days");
  const [selectedLanguage, setSelectedLanguage] = useState("all");
  const [analyticsData, setAnalyticsData] = useState(null);
  const [postsData, setPostsData] = useState([]);

  // Time range options
  const timeRangeOptions = [
    { value: "lastday", label: "Last day" },
    { value: "lastweek", label: "Last week" },
    { value: "last14days", label: "Last 14 days" },
    { value: "last30days", label: "Last 30 days" },
    { value: "last2months", label: "Last 2 months" },
    { value: "last4months", label: "Last 4 months" },
    { value: "last6months", label: "Last 6 months" },
  ];

  // Language options for analytics
  const languageOptions = [
    { value: "all", label: "All languages", flag: "🌐" },
    { value: "en", label: "English", flag: "🇬🇧" },
    { value: "et", label: "Estonian", flag: "🇪🇪" },
    { value: "fi", label: "Finnish", flag: "🇫🇮" },
    { value: "de", label: "German", flag: "🇩🇪" },
    { value: "sv", label: "Swedish", flag: "🇸🇪" },
  ];

  // Load analytics data
  useEffect(() => {
    const loadAnalyticsData = async () => {
      try {
        setLoading(true);
        setError("");

        // Check if user is authenticated
        const token = localStorage.getItem("adminToken");
        if (!token) {
          setError(
            "Authentication required. Please log in to access this page."
          );
          setLoading(false);
          return;
        }

        // Load analytics data
        const [analyticsResult, postsResult] = await Promise.all([
          adminAPI.getBlogAnalytics(timeRange, selectedLanguage),
          adminAPI.getBlogPostsAnalytics(timeRange, selectedLanguage),
        ]);

        // Handle analytics response
        if (analyticsResult.response.ok && analyticsResult.data) {
          setAnalyticsData(analyticsResult.data.data || analyticsResult.data);
        } else {
          console.error(
            "Analytics API failed:",
            analyticsResult.response.status,
            analyticsResult.response.statusText
          );
          if (
            analyticsResult.response.status === 401 ||
            analyticsResult.response.status === 403
          ) {
            setError("Authentication failed. Please log in again.");
            localStorage.removeItem("adminToken");
            return;
          }
          setError("Failed to load analytics data");
        }

        // Handle posts response
        if (postsResult.response.ok && postsResult.data) {
          setPostsData(postsResult.data.data || postsResult.data);
        } else {
          console.error(
            "Posts analytics API failed:",
            postsResult.response.status,
            postsResult.response.statusText
          );
          setPostsData([]);
        }
      } catch (error) {
        console.error("Error loading analytics data:", error);
        if (error.message && error.message.includes("fetch")) {
          setError(
            "Failed to connect to the server. Please check if the backend is running."
          );
        } else {
          setError("Failed to load analytics data. Please try again.");
        }
      } finally {
        setLoading(false);
      }
    };

    loadAnalyticsData();
  }, [timeRange, selectedLanguage]);

  const handleTimeRangeChange = (newTimeRange) => {
    setTimeRange(newTimeRange);
  };

  const handleLanguageChange = (newLanguage) => {
    setSelectedLanguage(newLanguage);
  };

  if (loading) {
    return (
      <AdminLayout title="Blog Analytics">
        <SEO
          title="Blog Analytics - Admin"
          description="Blog analytics and performance metrics"
        />
        <div
          className="d-flex justify-content-center align-items-center"
          style={{ minHeight: "400px" }}
        >
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title="Analytics Dashboard">
      <SEO
        title="Analytics Dashboard - Admin"
        description="Blog analytics, conversion tracking, and page performance metrics"
      />

      <div className="admin-content">
        <div className="admin-header mb-4">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h1 className="admin-title mb-2">Analytics Dashboard</h1>
              <p className="admin-subtitle text-muted mb-0">
                Track and analyze your blog performance, conversions, and page
                analytics.{" "}
                <a
                  href="https://developers.google.com/analytics/devguides/collection/ga4"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary"
                >
                  Learn more
                </a>
              </p>
            </div>
          </div>
        </div>

        {error && (
          <div className="alert alert-danger mb-4" role="alert">
            <iconify-icon
              icon="solar:danger-bold"
              className="me-2"
            ></iconify-icon>
            {error}
          </div>
        )}

        {/* Time Range and Language Selectors */}
        <div className="row mb-4">
          <div className="col-md-6">
            <TimeRangeSelector
              options={timeRangeOptions}
              value={timeRange}
              onChange={handleTimeRangeChange}
              comparedPeriod={analyticsData?.comparedPeriod}
            />
          </div>
          <div className="col-md-6">
            <LanguageSelector
              options={languageOptions}
              value={selectedLanguage}
              onChange={handleLanguageChange}
            />
          </div>
        </div>

        {/* Analytics Overview Cards */}
        {analyticsData && (
          <div className="row mb-4">
            <div className="col-12">
              <AnalyticsOverview
                data={analyticsData.overview}
                selectedLanguage={selectedLanguage}
              />
            </div>
          </div>
        )}

        {/* Charts Row */}
        <div className="row mb-4">
          {/* Main Analytics Chart */}
          <div className="col-lg-8 col-12 mb-4">
            {analyticsData && (
              <AnalyticsChart
                data={analyticsData.chartData}
                timeRange={timeRange}
                selectedLanguage={selectedLanguage}
              />
            )}
          </div>

          {/* Heatmap Chart */}
          <div className="col-lg-4 col-12 mb-4">
            {analyticsData && (
              <HeatmapChart
                data={analyticsData.heatmapData}
                title={`Post views by time of day${
                  selectedLanguage !== "all"
                    ? ` (${
                        languageOptions.find(
                          (l) => l.value === selectedLanguage
                        )?.label
                      })`
                    : ""
                }`}
                selectedLanguage={selectedLanguage}
              />
            )}
          </div>
        </div>

        {/* Posts Analytics Table */}
        <div className="row mb-4">
          <div className="col-12">
            <PostsTable
              data={postsData}
              loading={loading}
              timeRange={timeRange}
              selectedLanguage={selectedLanguage}
            />
          </div>
        </div>

        {/* Conversion Analytics Section */}
        <div className="row mb-4">
          <div className="col-12">
            <div className="card">
              <div className="card-header">
                <h3 className="card-title mb-0">
                  <iconify-icon
                    icon="solar:target-bold"
                    className="me-2"
                  ></iconify-icon>
                  Conversion Analytics
                </h3>
                <p className="text-muted mb-0 mt-1">
                  Track Business Comanager CTA performance and conversion
                  metrics
                </p>
              </div>
              <div className="card-body">
                <ConversionAnalytics
                  timeRange={timeRange}
                  selectedLanguage={selectedLanguage}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Static Pages Analytics Section */}
        <div className="row mb-4">
          <div className="col-12">
            <div className="card">
              <div className="card-header">
                <h3 className="card-title mb-0">
                  <iconify-icon
                    icon="solar:document-bold"
                    className="me-2"
                  ></iconify-icon>
                  Static Pages Analytics
                </h3>
                <p className="text-muted mb-0 mt-1">
                  Performance metrics for all application pages
                </p>
              </div>
              <div className="card-body">
                <StaticPagesAnalytics
                  timeRange={timeRange}
                  selectedLanguage={selectedLanguage}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
};

export default AdminBlogAnalytics;
