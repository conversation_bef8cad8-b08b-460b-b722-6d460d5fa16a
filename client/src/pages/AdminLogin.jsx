import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import SEO from "../components/common/SEO";
import { authAPI } from "../utils/api";

const AdminLogin = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    // Clear error when user starts typing
    if (error) setError("");
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError("");

    try {
      const { response, data } = await authAPI.login(formData);

      if (data.success) {
        // Store token in localStorage
        localStorage.setItem("adminToken", data.token);
        localStorage.setItem("adminUser", JSON.stringify(data.user));

        // Redirect to admin dashboard
        navigate("/admin/dashboard");
      } else {
        setError(data.message || "Login failed");
      }
    } catch (err) {
      console.error("Login error:", err);
      setError("Network error. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <SEO
        title="Admin Login - DevSkills"
        description="Admin login page for DevSkills content management"
        noIndex={true}
      />

      {/* Page Wrapper */}
      <div id="page" className="page">
        {/* Main Content */}
        <main id="main">
          {/* Admin Login Section */}
          <section
            className="page-section bg-dark-1 bg-dark-alpha-80 light-content admin-login-section"
            id="admin-login"
          >
            <div className="container relative">
              <div className="row">
                <div className="col-md-6 offset-md-3 col-lg-4 offset-lg-4">
                  {/* Login Form */}
                  <div className="form-container">
                    {/* Header */}
                    <div className="text-center mb-60 mb-sm-40">
                      <div className="hs-line-4 font-alt black mb-20 mb-xs-10">
                        <span className="color-primary-1">DevSkills</span> Admin
                      </div>
                      <p className="section-descr mb-0">
                        Sign in to access the admin dashboard
                      </p>
                    </div>

                    {/* Error Message */}
                    {error && (
                      <div className="alert alert-danger mb-30" role="alert">
                        <i className="mi-warning"></i>
                        {error}
                      </div>
                    )}

                    {/* Login Form */}
                    <form className="form contact-form" onSubmit={handleSubmit}>
                      {/* Email Field */}
                      <div className="form-group">
                        <label htmlFor="email" className="sr-only">
                          Email Address
                        </label>
                        <input
                          type="email"
                          name="email"
                          id="email"
                          className="input-lg round form-control"
                          placeholder="Email Address"
                          value={formData.email}
                          onChange={handleChange}
                          required
                          autoComplete="email"
                        />
                      </div>

                      {/* Password Field */}
                      <div className="form-group">
                        <label htmlFor="password" className="sr-only">
                          Password
                        </label>
                        <input
                          type="password"
                          name="password"
                          id="password"
                          className="input-lg round form-control"
                          placeholder="Password"
                          value={formData.password}
                          onChange={handleChange}
                          required
                          autoComplete="current-password"
                        />
                      </div>

                      {/* Submit Button */}
                      <div className="form-group">
                        <button
                          type="submit"
                          className="btn btn-mod btn-color btn-large btn-round btn-full-width"
                          disabled={loading}
                        >
                          {loading ? (
                            <>
                              <i className="fa fa-spinner fa-spin me-2"></i>
                              Signing in...
                            </>
                          ) : (
                            <>
                              <i className="mi-lock me-2"></i>
                              Sign In
                            </>
                          )}
                        </button>
                      </div>
                    </form>

                    {/* Footer */}
                    <div className="text-center mt-40">
                      <p className="small opacity-07">
                        © 2024 DevSkills. All rights reserved.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </main>
      </div>
    </>
  );
};

export default AdminLogin;
