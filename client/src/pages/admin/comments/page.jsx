import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import AdminLayout from '@/components/admin/AdminLayout';
import { getAdminComments, approveComment, rejectComment, deleteComment } from '@/utils/commentAPI';

export default function AdminCommentsPage() {
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [stats, setStats] = useState({ total: 0, pending: 0, approved: 0 });

  const fetchComments = async () => {
    try {
      setLoading(true);
      const response = await getAdminComments({
        page: currentPage,
        limit: 10,
        status: statusFilter,
        search: searchTerm,
      });

      if (response.success) {
        setComments(response.data.comments);
        setTotalPages(response.data.pagination.pages);
        
        // Update stats
        const totalComments = response.data.pagination.total;
        setStats(prev => ({ ...prev, total: totalComments }));
      }
    } catch (err) {
      setError('Failed to fetch comments');
      console.error('Fetch comments error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchComments();
  }, [currentPage, statusFilter, searchTerm]);

  const handleApprove = async (commentId) => {
    try {
      await approveComment(commentId);
      fetchComments(); // Refresh the list
    } catch (err) {
      setError('Failed to approve comment');
    }
  };

  const handleReject = async (commentId) => {
    try {
      await rejectComment(commentId);
      fetchComments(); // Refresh the list
    } catch (err) {
      setError('Failed to reject comment');
    }
  };

  const handleDelete = async (commentId) => {
    if (window.confirm('Are you sure you want to delete this comment? This action cannot be undone.')) {
      try {
        await deleteComment(commentId);
        fetchComments(); // Refresh the list
      } catch (err) {
        setError('Failed to delete comment');
      }
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchComments();
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const truncateText = (text, maxLength = 100) => {
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  };

  return (
    <AdminLayout>
      <div className="container-fluid">
        <div className="row">
          <div className="col-12">
            <div className="d-flex justify-content-between align-items-center mb-4">
              <h1 className="h3 mb-0">Comment Management</h1>
              <div className="d-flex gap-3">
                <div className="badge bg-primary">Total: {stats.total}</div>
                <div className="badge bg-warning">Pending: {stats.pending}</div>
                <div className="badge bg-success">Approved: {stats.approved}</div>
              </div>
            </div>

            {error && (
              <div className="alert alert-danger" role="alert">
                {error}
              </div>
            )}

            {/* Filters */}
            <div className="card mb-4">
              <div className="card-body">
                <div className="row g-3">
                  <div className="col-md-4">
                    <label className="form-label">Status Filter</label>
                    <select
                      className="form-select"
                      value={statusFilter}
                      onChange={(e) => {
                        setStatusFilter(e.target.value);
                        setCurrentPage(1);
                      }}
                    >
                      <option value="all">All Comments</option>
                      <option value="pending">Pending Approval</option>
                      <option value="approved">Approved</option>
                    </select>
                  </div>
                  <div className="col-md-8">
                    <label className="form-label">Search</label>
                    <form onSubmit={handleSearch} className="d-flex">
                      <input
                        type="text"
                        className="form-control"
                        placeholder="Search by author name, email, or content..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                      <button type="submit" className="btn btn-primary ms-2">
                        Search
                      </button>
                    </form>
                  </div>
                </div>
              </div>
            </div>

            {/* Comments Table */}
            <div className="card">
              <div className="card-body">
                {loading ? (
                  <div className="text-center py-4">
                    <div className="spinner-border" role="status">
                      <span className="visually-hidden">Loading...</span>
                    </div>
                  </div>
                ) : comments.length === 0 ? (
                  <div className="text-center py-4">
                    <p className="text-muted">No comments found.</p>
                  </div>
                ) : (
                  <div className="table-responsive">
                    <table className="table table-hover">
                      <thead>
                        <tr>
                          <th>Author</th>
                          <th>Email</th>
                          <th>Content</th>
                          <th>Blog Post</th>
                          <th>Status</th>
                          <th>Date</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {comments.map((comment) => (
                          <tr key={comment.id}>
                            <td>
                              <strong>{comment.author}</strong>
                              {comment.website && (
                                <div>
                                  <small>
                                    <a href={comment.website} target="_blank" rel="noopener noreferrer">
                                      {comment.website}
                                    </a>
                                  </small>
                                </div>
                              )}
                            </td>
                            <td>
                              <small className="text-muted">{comment.email}</small>
                            </td>
                            <td>
                              <div title={comment.content}>
                                {truncateText(comment.content)}
                              </div>
                              {comment.parent && (
                                <small className="text-muted">
                                  Reply to: {comment.parent.author}
                                </small>
                              )}
                            </td>
                            <td>
                              <Link 
                                to={`/blog-single/${comment.blogPost.slug}`}
                                className="text-decoration-none"
                                target="_blank"
                              >
                                {comment.blogPost.translations[0]?.title || 'Untitled'}
                              </Link>
                            </td>
                            <td>
                              <span className={`badge ${comment.approved ? 'bg-success' : 'bg-warning'}`}>
                                {comment.approved ? 'Approved' : 'Pending'}
                              </span>
                            </td>
                            <td>
                              <small>{formatDate(comment.createdAt)}</small>
                            </td>
                            <td>
                              <div className="btn-group btn-group-sm">
                                {!comment.approved ? (
                                  <button
                                    className="btn btn-success"
                                    onClick={() => handleApprove(comment.id)}
                                    title="Approve"
                                  >
                                    <i className="mi-check"></i>
                                  </button>
                                ) : (
                                  <button
                                    className="btn btn-warning"
                                    onClick={() => handleReject(comment.id)}
                                    title="Hide"
                                  >
                                    <i className="mi-eye-off"></i>
                                  </button>
                                )}
                                <button
                                  className="btn btn-danger"
                                  onClick={() => handleDelete(comment.id)}
                                  title="Delete"
                                >
                                  <i className="mi-trash"></i>
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}

                {/* Pagination */}
                {totalPages > 1 && (
                  <nav className="mt-4">
                    <ul className="pagination justify-content-center">
                      <li className={`page-item ${currentPage === 1 ? 'disabled' : ''}`}>
                        <button
                          className="page-link"
                          onClick={() => setCurrentPage(currentPage - 1)}
                          disabled={currentPage === 1}
                        >
                          Previous
                        </button>
                      </li>
                      {[...Array(totalPages)].map((_, index) => (
                        <li key={index + 1} className={`page-item ${currentPage === index + 1 ? 'active' : ''}`}>
                          <button
                            className="page-link"
                            onClick={() => setCurrentPage(index + 1)}
                          >
                            {index + 1}
                          </button>
                        </li>
                      ))}
                      <li className={`page-item ${currentPage === totalPages ? 'disabled' : ''}`}>
                        <button
                          className="page-link"
                          onClick={() => setCurrentPage(currentPage + 1)}
                          disabled={currentPage === totalPages}
                        >
                          Next
                        </button>
                      </li>
                    </ul>
                  </nav>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
