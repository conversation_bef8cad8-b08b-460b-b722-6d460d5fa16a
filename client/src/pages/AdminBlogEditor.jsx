import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import SEO from "../components/common/SEO";
import AdminLayout from "../components/admin/AdminLayout";
import TipTapEditor from "../components/editor/TipTapEditor";
import { adminAPI, blogAPI, API_BASE_URL } from "../utils/api";

const AdminBlogEditor = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams(); // For editing existing posts
  const isEditing = Boolean(id);

  // Utility function to construct image URLs
  const getImageUrl = (filename) => {
    if (!filename) return null;

    // If it's already a full URL, return as is
    if (filename.startsWith("http")) {
      return filename;
    }

    // Construct the full URL for uploaded images
    const baseUrl = API_BASE_URL.replace("/api", "");
    return `${baseUrl}/uploads/blog-images/${filename}`;
  };

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  // Available languages from i18n (memoized to prevent infinite re-renders)
  const [availableLanguages] = useState(() => Object.keys(i18n.store.data));

  // Form state
  const [formData, setFormData] = useState(() => {
    // Initialize translations for all available languages
    const initialTranslations = {};
    availableLanguages.forEach((lang) => {
      initialTranslations[lang] = {
        title: "",
        excerpt: "",
        content: "",
        metaTitle: "",
        metaDesc: "",
        keywords: [],
      };
    });

    return {
      slug: "",
      featured: false,
      published: false,
      scheduledAt: "",
      featuredImage: null,
      featuredImageAlt: "",
      readTime: "",
      categoryIds: [],
      tagIds: [],
      translations: initialTranslations,
    };
  });

  const [activeLanguage, setActiveLanguage] = useState("en");
  const [categories, setCategories] = useState([]);
  const [tags, setTags] = useState([]);
  const [imagePreview, setImagePreview] = useState(null);

  // Load categories and tags
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError("");

        // Check if user is authenticated
        const token = localStorage.getItem("adminToken");
        if (!token) {
          setError(
            "Authentication required. Please log in to access this page."
          );
          setLoading(false);
          return;
        }

        // Load categories and tags using the API utility
        const [categoriesResult, tagsResult] = await Promise.all([
          adminAPI.getCategories(),
          adminAPI.getTags(),
        ]);

        // Handle categories response
        if (categoriesResult.response.ok && categoriesResult.data) {
          setCategories(categoriesResult.data.data || []);
        } else {
          console.error(
            "Categories API failed:",
            categoriesResult.response.status,
            categoriesResult.response.statusText
          );
          if (
            categoriesResult.response.status === 401 ||
            categoriesResult.response.status === 403
          ) {
            setError("Authentication failed. Please log in again.");
            localStorage.removeItem("adminToken");
            return;
          }
          setCategories([]);
        }

        // Handle tags response
        if (tagsResult.response.ok && tagsResult.data) {
          setTags(tagsResult.data.data || []);
        } else {
          console.error(
            "Tags API failed:",
            tagsResult.response.status,
            tagsResult.response.statusText
          );
          if (
            tagsResult.response.status === 401 ||
            tagsResult.response.status === 403
          ) {
            setError("Authentication failed. Please log in again.");
            localStorage.removeItem("adminToken");
            return;
          }
          setTags([]);
        }

        // Load existing post if editing
        if (isEditing) {
          const { response: postRes, data: postData } = await adminAPI.getPost(
            id
          );

          if (postRes.ok && postData.success) {
            try {
              const post = postData.data;

              // Convert translations array to object
              const translationsObj = {};
              if (post.translations && Array.isArray(post.translations)) {
                post.translations.forEach((t) => {
                  translationsObj[t.language] = t;
                });
              }

              setFormData((prev) => ({
                ...prev,
                slug: post.slug || "",
                featured: post.featured || false,
                published: post.published || false,
                scheduledAt: post.scheduledAt
                  ? new Date(post.scheduledAt).toISOString().slice(0, 16)
                  : "",
                featuredImage: null,
                featuredImageAlt: post.featuredImageAlt || "",
                readTime: post.readTime || "",
                categoryIds: post.categories
                  ? post.categories.map((c) => c.id)
                  : [],
                tagIds: post.tags ? post.tags.map((t) => t.id) : [],
                translations: { ...prev.translations, ...translationsObj },
              }));

              if (post.featuredImage) {
                setImagePreview(getImageUrl(post.featuredImage));
              }
            } catch (jsonError) {
              console.error("Failed to parse post response:", jsonError);
              setError("Failed to load post data - invalid response format");
            }
          } else {
            console.error(
              "Post API failed:",
              postRes.status,
              postRes.statusText
            );
            setError(
              postData.message ||
                `Failed to load post: ${postRes.status} ${postRes.statusText}`
            );
          }
        }
      } catch (error) {
        console.error("Error loading data:", error);
        if (error.message && error.message.includes("fetch")) {
          setError(
            "Failed to connect to the server. Please check if the backend is running on localhost:4004"
          );
        } else {
          setError("Failed to load data. Please try again.");
        }
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [id, isEditing]);

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleTranslationChange = (language, field, value) => {
    setFormData((prev) => ({
      ...prev,
      translations: {
        ...prev.translations,
        [language]: {
          ...prev.translations[language],
          [field]: value,
        },
      },
    }));
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData((prev) => ({
        ...prev,
        featuredImage: file,
      }));

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError("");
    setSuccess("");

    try {
      const token = localStorage.getItem("adminToken");
      const formDataToSend = new FormData();

      // Add basic fields
      formDataToSend.append("slug", formData.slug);
      formDataToSend.append("featured", formData.featured);
      formDataToSend.append("published", formData.published);
      if (formData.scheduledAt) {
        formDataToSend.append("scheduledAt", formData.scheduledAt);
      }
      formDataToSend.append("featuredImageAlt", formData.featuredImageAlt);
      if (formData.readTime) {
        formDataToSend.append("readTime", formData.readTime);
      }

      // Add arrays
      formDataToSend.append(
        "categoryIds",
        JSON.stringify(formData.categoryIds)
      );
      formDataToSend.append("tagIds", JSON.stringify(formData.tagIds));
      formDataToSend.append(
        "translations",
        JSON.stringify(formData.translations)
      );

      // Add image if selected
      if (formData.featuredImage) {
        formDataToSend.append("featuredImage", formData.featuredImage);
      }

      // Use the proper API utility
      let result;
      if (isEditing) {
        result = await blogAPI.updatePost(id, formDataToSend);
      } else {
        result = await blogAPI.createPost(formDataToSend);
      }

      const { response, data } = result;

      if (response.ok && data && data.success) {
        setSuccess(
          `Blog post ${isEditing ? "updated" : "created"} successfully!`
        );
        setTimeout(() => {
          navigate("/admin/posts");
        }, 2000);
      } else {
        const errorMessage =
          data?.message ||
          `Failed to ${isEditing ? "update" : "create"} blog post`;
        setError(errorMessage);
      }
    } catch (error) {
      console.error("Save error:", error);
      setError("Network error. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  return (
    <>
      <SEO
        title={`${isEditing ? "Edit" : "Create"} Blog Post - Admin`}
        description="Create or edit blog posts in the admin panel"
        noIndex={true}
      />

      <AdminLayout
        title={isEditing ? "Edit Blog Post" : "Create New Blog Post"}
      >
        <form onSubmit={handleSubmit} className="admin-form">
          {/* Messages */}
          {error && (
            <div className="alert alert-danger mb-30" role="alert">
              <iconify-icon
                icon="solar:danger-triangle-bold"
                className="me-2"
              ></iconify-icon>
              {error}
            </div>
          )}

          {success && (
            <div className="alert alert-success mb-30" role="alert">
              <iconify-icon
                icon="solar:check-circle-bold"
                className="me-2"
              ></iconify-icon>
              {success}
            </div>
          )}

          {/* Basic Settings */}
          <div className="admin-table mb-40">
            <div className="row mb-30">
              <div className="col-12">
                <h3 className="hs-line-4 font-alt black mb-0">
                  <iconify-icon
                    icon="solar:settings-bold"
                    className="me-2 color-primary-1"
                  ></iconify-icon>
                  Basic Settings
                </h3>
                <p className="section-descr mb-0">
                  Configure the basic properties of your blog post
                </p>
              </div>
            </div>

            <div className="row">
              <div className="col-md-6 mb-30">
                <label className="form-label">
                  <iconify-icon
                    icon="solar:link-bold"
                    className="me-2"
                  ></iconify-icon>
                  Slug (URL)
                </label>
                <input
                  type="text"
                  value={formData.slug}
                  onChange={(e) => handleInputChange("slug", e.target.value)}
                  className="form-control"
                  placeholder="blog-post-url"
                />
                <small className="form-text text-muted">
                  This will be the URL path for your blog post (e.g.,
                  /blog/your-slug)
                </small>
              </div>

              <div className="col-md-6 mb-30">
                <label className="form-label">
                  <iconify-icon
                    icon="solar:clock-circle-bold"
                    className="me-2"
                  ></iconify-icon>
                  Read Time (minutes)
                </label>
                <input
                  type="number"
                  value={formData.readTime}
                  onChange={(e) =>
                    handleInputChange("readTime", e.target.value)
                  }
                  className="form-control"
                  placeholder="5"
                  min="1"
                  max="60"
                />
                <small className="form-text text-muted">
                  Estimated reading time for this post
                </small>
              </div>

              <div className="col-md-6 mb-30">
                <label className="form-label">
                  <iconify-icon
                    icon="solar:calendar-bold"
                    className="me-2"
                  ></iconify-icon>
                  Schedule Publication
                </label>
                <input
                  type="datetime-local"
                  value={formData.scheduledAt}
                  onChange={(e) =>
                    handleInputChange("scheduledAt", e.target.value)
                  }
                  className="form-control"
                />
                <small className="form-text text-muted">
                  Leave empty to publish immediately when published is checked
                </small>
              </div>

              <div className="col-md-6 mb-30">
                <label className="form-label">
                  <iconify-icon
                    icon="solar:star-bold"
                    className="me-2"
                  ></iconify-icon>
                  Post Options
                </label>
                <div className="d-flex flex-column gap-2">
                  <div className="form-check">
                    <input
                      type="checkbox"
                      id="featured"
                      checked={formData.featured}
                      onChange={(e) =>
                        handleInputChange("featured", e.target.checked)
                      }
                      className="form-check-input"
                    />
                    <label className="form-check-label" htmlFor="featured">
                      <iconify-icon
                        icon="solar:star-bold"
                        className="me-1"
                      ></iconify-icon>
                      Featured Post
                    </label>
                    <small className="form-text text-muted d-block">
                      Show this post prominently on the homepage
                    </small>
                  </div>

                  <div className="form-check">
                    <input
                      type="checkbox"
                      id="published"
                      checked={formData.published}
                      onChange={(e) =>
                        handleInputChange("published", e.target.checked)
                      }
                      className="form-check-input"
                    />
                    <label className="form-check-label" htmlFor="published">
                      <iconify-icon
                        icon="solar:check-circle-bold"
                        className="me-1"
                      ></iconify-icon>
                      Published
                    </label>
                    <small className="form-text text-muted d-block">
                      Make this post visible to the public
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Featured Image */}
          <div className="admin-table mb-40">
            <div className="row mb-30">
              <div className="col-12">
                <h3 className="hs-line-4 font-alt black mb-0">
                  <iconify-icon
                    icon="solar:gallery-bold"
                    className="me-2 color-primary-1"
                  ></iconify-icon>
                  Featured Image
                </h3>
                <p className="section-descr mb-0">
                  Upload a featured image that will be displayed with your blog
                  post
                </p>
              </div>
            </div>

            <div className="row">
              <div className="col-md-6 mb-30">
                <label className="form-label">
                  <iconify-icon
                    icon="solar:upload-bold"
                    className="me-2"
                  ></iconify-icon>
                  Upload Image
                </label>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                  className="form-control"
                />
                <small className="form-text text-muted">
                  Recommended size: 1200x630px. Supported formats: JPG, PNG,
                  WebP
                </small>
              </div>

              <div className="col-md-6 mb-30">
                <label className="form-label">
                  <iconify-icon
                    icon="solar:eye-bold"
                    className="me-2"
                  ></iconify-icon>
                  Alt Text
                </label>
                <input
                  type="text"
                  value={formData.featuredImageAlt}
                  onChange={(e) =>
                    handleInputChange("featuredImageAlt", e.target.value)
                  }
                  className="form-control"
                  placeholder="Describe the image for accessibility"
                />
                <small className="form-text text-muted">
                  Describe the image for screen readers and SEO
                </small>
              </div>

              {imagePreview && (
                <div className="col-12">
                  <div className="mb-20">
                    <label className="form-label">Image Preview</label>
                  </div>
                  <div className="text-center">
                    <img
                      src={imagePreview}
                      alt="Preview"
                      className="image-preview"
                      style={{ maxWidth: "400px", height: "auto" }}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Language Tabs */}
          <div className="admin-table mb-40">
            <div className="row mb-30">
              <div className="col-12">
                <h3 className="hs-line-4 font-alt black mb-0">
                  <i className="mi-globe me-2 color-primary-1"></i>
                  Content (Multi-language)
                </h3>
                <p className="section-descr mb-0">
                  Create content in multiple languages. At least English content
                  is required.
                </p>
              </div>
            </div>

            {/* Language Selector */}
            <div className="language-tabs mb-30">
              {availableLanguages.map((lang) => (
                <button
                  key={lang}
                  type="button"
                  onClick={() => setActiveLanguage(lang)}
                  className={`language-tab ${
                    activeLanguage === lang ? "active" : ""
                  }`}
                >
                  <i className="mi-globe me-2"></i>
                  {lang.toUpperCase()}
                  {lang === "en" && (
                    <span className="ms-1 small">(Required)</span>
                  )}
                </button>
              ))}
            </div>

            {/* Content for Active Language */}
            <div className="row">
              <div className="col-12 mb-30">
                <label className="form-label">
                  <i className="mi-edit me-2"></i>
                  Title ({activeLanguage.toUpperCase()})
                  {activeLanguage === "en" && (
                    <span className="text-danger ms-1">*</span>
                  )}
                </label>
                <input
                  type="text"
                  value={formData.translations[activeLanguage]?.title || ""}
                  onChange={(e) =>
                    handleTranslationChange(
                      activeLanguage,
                      "title",
                      e.target.value
                    )
                  }
                  className="form-control"
                  placeholder="Enter blog post title"
                  required={activeLanguage === "en"}
                />
                <small className="form-text text-muted">
                  The main title of your blog post in{" "}
                  {activeLanguage.toUpperCase()}
                </small>
              </div>

              <div className="col-12 mb-30">
                <label className="form-label">
                  <i className="mi-text me-2"></i>
                  Excerpt ({activeLanguage.toUpperCase()})
                </label>
                <textarea
                  value={formData.translations[activeLanguage]?.excerpt || ""}
                  onChange={(e) =>
                    handleTranslationChange(
                      activeLanguage,
                      "excerpt",
                      e.target.value
                    )
                  }
                  rows={3}
                  className="form-control"
                  placeholder="Brief description of the blog post"
                />
                <small className="form-text text-muted">
                  A short summary that will appear in blog listings and social
                  media previews
                </small>
              </div>

              <div className="col-12 mb-30">
                <label className="form-label">
                  <iconify-icon
                    icon="solar:document-text-bold"
                    className="me-2"
                  ></iconify-icon>
                  Content ({activeLanguage.toUpperCase()})
                  {activeLanguage === "en" && (
                    <span className="text-danger ms-1">*</span>
                  )}
                </label>
                <TipTapEditor
                  content={formData.translations[activeLanguage]?.content || ""}
                  onChange={(html) =>
                    handleTranslationChange(activeLanguage, "content", html)
                  }
                  placeholder="Write your blog post content here. You can paste formatted text and code snippets with syntax highlighting."
                />
                <small className="form-text text-muted">
                  <iconify-icon
                    icon="solar:info-circle-bold"
                    className="me-1"
                  ></iconify-icon>
                  Rich text editor with syntax highlighting. Paste code snippets
                  and they will be automatically highlighted. Use the toolbar
                  for formatting options.
                </small>
              </div>

              <div className="col-md-6 mb-30">
                <label className="form-label">
                  <i className="mi-seo me-2"></i>
                  Meta Title ({activeLanguage.toUpperCase()})
                </label>
                <input
                  type="text"
                  value={formData.translations[activeLanguage]?.metaTitle || ""}
                  onChange={(e) =>
                    handleTranslationChange(
                      activeLanguage,
                      "metaTitle",
                      e.target.value
                    )
                  }
                  className="form-control"
                  placeholder="SEO title (optional)"
                  maxLength="60"
                />
                <small className="form-text text-muted">
                  <i className="mi-search me-1"></i>
                  Title that appears in search engine results (max 60
                  characters)
                </small>
              </div>

              <div className="col-md-6 mb-30">
                <label className="form-label">
                  <i className="mi-description me-2"></i>
                  Meta Description ({activeLanguage.toUpperCase()})
                </label>
                <textarea
                  value={formData.translations[activeLanguage]?.metaDesc || ""}
                  onChange={(e) =>
                    handleTranslationChange(
                      activeLanguage,
                      "metaDesc",
                      e.target.value
                    )
                  }
                  rows={3}
                  className="form-control"
                  placeholder="SEO description (optional)"
                  maxLength="160"
                />
                <small className="form-text text-muted">
                  <i className="mi-search me-1"></i>
                  Description that appears in search engine results (max 160
                  characters)
                </small>
              </div>
            </div>
          </div>

          {/* Categories and Tags */}
          <div className="admin-table mb-40">
            <div className="row mb-30">
              <div className="col-12">
                <h3 className="hs-line-4 font-alt black mb-0">
                  <iconify-icon
                    icon="solar:tag-bold"
                    className="me-2 color-primary-1"
                  ></iconify-icon>
                  Categories & Tags
                </h3>
                <p className="section-descr mb-0">
                  Organize your blog post with categories and tags
                </p>
              </div>
            </div>

            <div className="row">
              <div className="col-md-6 mb-30">
                <label className="form-label">
                  <iconify-icon
                    icon="solar:folder-bold"
                    className="me-2"
                  ></iconify-icon>
                  Categories
                </label>
                <div className="categories-grid">
                  {categories && categories.length > 0 ? (
                    categories.map((category) => (
                      <div key={category.id} className="form-check mb-2">
                        <input
                          className="form-check-input"
                          type="checkbox"
                          id={`category-${category.id}`}
                          checked={formData.categoryIds.includes(category.id)}
                          onChange={() => {
                            const newCategoryIds =
                              formData.categoryIds.includes(category.id)
                                ? formData.categoryIds.filter(
                                    (id) => id !== category.id
                                  )
                                : [...formData.categoryIds, category.id];
                            setFormData((prev) => ({
                              ...prev,
                              categoryIds: newCategoryIds,
                            }));
                          }}
                        />
                        <label
                          className="form-check-label"
                          htmlFor={`category-${category.id}`}
                        >
                          {category.name}
                        </label>
                      </div>
                    ))
                  ) : (
                    <p className="text-muted">No categories available</p>
                  )}
                </div>
              </div>

              <div className="col-md-6 mb-30">
                <label className="form-label">
                  <iconify-icon
                    icon="solar:hashtag-bold"
                    className="me-2"
                  ></iconify-icon>
                  Tags
                </label>
                <div className="tags-grid">
                  {tags && tags.length > 0 ? (
                    tags.map((tag) => (
                      <div key={tag.id} className="form-check mb-2">
                        <input
                          className="form-check-input"
                          type="checkbox"
                          id={`tag-${tag.id}`}
                          checked={formData.tagIds.includes(tag.id)}
                          onChange={() => {
                            const newTagIds = formData.tagIds.includes(tag.id)
                              ? formData.tagIds.filter((id) => id !== tag.id)
                              : [...formData.tagIds, tag.id];
                            setFormData((prev) => ({
                              ...prev,
                              tagIds: newTagIds,
                            }));
                          }}
                        />
                        <label
                          className="form-check-label"
                          htmlFor={`tag-${tag.id}`}
                        >
                          {tag.name}
                        </label>
                      </div>
                    ))
                  ) : (
                    <p className="text-muted">No tags available</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="row mt-40">
            <div className="col-12 text-end">
              <button
                type="button"
                onClick={() => navigate("/admin/posts")}
                className="btn btn-mod btn-gray btn-round me-3"
              >
                Cancel
              </button>

              <button
                type="submit"
                disabled={saving}
                className="btn btn-mod btn-color btn-round"
              >
                {saving ? (
                  <>
                    <i className="fa fa-spinner fa-spin me-2"></i>
                    Saving...
                  </>
                ) : (
                  <>
                    <i className="mi-check me-2"></i>
                    {isEditing ? "Update Post" : "Create Post"}
                  </>
                )}
              </button>
            </div>
          </div>
        </form>
      </AdminLayout>
    </>
  );
};

export default AdminBlogEditor;
