import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import SEO from "../components/common/SEO";
import AdminLayout from "../components/admin/AdminLayout";
import TipTapEditor from "../components/editor/TipTapEditor";
import { adminAPI, API_BASE_URL } from "../utils/api";

const AdminProductEditor = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams(); // For editing existing products
  const isEditing = Boolean(id);

  // Utility function to construct image URLs
  const getImageUrl = (filename) => {
    if (!filename) return null;

    // If it's already a full URL, return as is
    if (filename.startsWith("http")) {
      return filename;
    }

    // Construct the full URL for uploaded images
    const baseUrl = API_BASE_URL.replace("/api", "");
    return `${baseUrl}/uploads/product-images/${filename}`;
  };

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState("");
  const [success, setSuccess] = useState("");

  // Available languages from i18n (memoized to prevent infinite re-renders)
  const [availableLanguages] = useState(() => Object.keys(i18n.store.data));

  // Form state
  const [formData, setFormData] = useState(() => {
    // Initialize translations for all available languages
    const initialTranslations = {};
    availableLanguages.forEach((lang) => {
      initialTranslations[lang] = {
        title: "",
        excerpt: "",
        content: "",
        metaTitle: "",
        metaDesc: "",
        keywords: [],
      };
    });

    return {
      slug: "",
      whitelabelPrice: "",
      subscriptionPrice: "",
      demoUrl: "",
      status: "draft",
      featuredImage: null,
      featuredImageAlt: "",
      images: [], // Array of image objects
      categoryIds: [],
      tagIds: [],
      translations: initialTranslations,
    };
  });

  const [categories, setCategories] = useState([]);
  const [tags, setTags] = useState([]);
  const [activeLanguage, setActiveLanguage] = useState("en");
  const [imagePreview, setImagePreview] = useState(null);
  const [selectedImages, setSelectedImages] = useState([]);
  const [displayImageIndex, setDisplayImageIndex] = useState(0);

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        // Load categories and tags
        const [categoriesResult, tagsResult] = await Promise.all([
          adminAPI.getCategories(),
          adminAPI.getTags(),
        ]);

        if (categoriesResult.data?.success) {
          setCategories(categoriesResult.data.data);
        }

        if (tagsResult.data?.success) {
          setTags(tagsResult.data.data);
        }

        // Load existing product if editing
        if (isEditing && id) {
          console.log("Loading product with ID:", id);
          console.log("Calling adminAPI.getProduct...");
          const productResult = await adminAPI.getProduct(id);
          console.log("Product API response:", productResult);

          if (productResult.data?.success) {
            const product = productResult.data.product;
            console.log("Product data:", product);

            // Set basic fields
            setFormData((prev) => ({
              ...prev,
              slug: product.slug || "",
              whitelabelPrice: product.whitelabelPrice || "",
              subscriptionPrice: product.subscriptionPrice || "",
              demoUrl: product.demoUrl || "",
              status: product.status || "draft",
              featuredImageAlt: product.featuredImageAlt || "",
              categoryIds: product.categories?.map((c) => c.categoryId) || [],
              tagIds: product.tags?.map((t) => t.tagId) || [],
            }));

            // Handle translations - products have translations array, not single language
            const updatedTranslations = { ...formData.translations };
            if (product.translations && Array.isArray(product.translations)) {
              product.translations.forEach((translation) => {
                updatedTranslations[translation.language] = {
                  title: translation.title || "",
                  excerpt: translation.excerpt || "",
                  content: translation.content || "",
                  metaTitle: translation.metaTitle || "",
                  metaDesc: translation.metaDesc || "",
                  keywords: translation.keywords || [],
                };
              });
            }

            setFormData((prev) => ({
              ...prev,
              translations: updatedTranslations,
            }));

            // Load existing images
            if (product.images && Array.isArray(product.images)) {
              console.log("Loading existing images:", product.images);
              const existingImages = product.images.map((img, index) => {
                const imageUrl = getImageUrl(img.filename);
                console.log(`Image ${index}: ${img.filename} -> ${imageUrl}`);
                return {
                  id: img.id,
                  file: null, // No file for existing images
                  preview: imageUrl,
                  alt: img.alt || "",
                  isDisplay: img.isDisplay,
                  filename: img.filename, // Store filename for existing images
                  sortOrder: img.sortOrder,
                };
              });
              console.log("Processed existing images:", existingImages);
              setSelectedImages(existingImages);

              // Set display image index
              const displayIndex = existingImages.findIndex(
                (img) => img.isDisplay
              );
              if (displayIndex !== -1) {
                setDisplayImageIndex(displayIndex);
              }
            } else {
              console.log("No images found in product:", product.images);
            }

            // Set legacy featured image preview if exists (fallback)
            if (
              product.featuredImage &&
              (!product.images || product.images.length === 0)
            ) {
              setImagePreview(getImageUrl(product.featuredImage));
            }
          } else {
            console.error("Failed to load product:", productResult);
            setError("Failed to load product data");
          }
        }
      } catch (error) {
        console.error("Error loading data:", error);
        setError("Failed to load data");
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [id, isEditing]);

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleTranslationChange = (language, field, value) => {
    setFormData((prev) => ({
      ...prev,
      translations: {
        ...prev.translations,
        [language]: {
          ...prev.translations[language],
          [field]: value,
        },
      },
    }));
  };

  const handleImagesChange = (e) => {
    const files = Array.from(e.target.files);
    if (files.length === 0) return;

    // Check if total images would exceed 10
    if (selectedImages.length + files.length > 10) {
      setError("Maximum 10 images allowed per product");
      return;
    }

    const newImages = files.map((file, index) => ({
      file,
      preview: URL.createObjectURL(file),
      alt: "",
      isDisplay: selectedImages.length === 0 && index === 0, // First image of first upload is display
    }));

    setSelectedImages((prev) => [...prev, ...newImages]);
    setError(""); // Clear any previous errors
  };

  const removeImage = (index) => {
    setSelectedImages((prev) => {
      const updated = prev.filter((_, i) => i !== index);
      // If we removed the display image, make the first image the display
      if (prev[index]?.isDisplay && updated.length > 0) {
        updated[0].isDisplay = true;
        setDisplayImageIndex(0);
      }
      return updated;
    });
  };

  const setDisplayImage = (index) => {
    setSelectedImages((prev) =>
      prev.map((img, i) => ({
        ...img,
        isDisplay: i === index,
      }))
    );
    setDisplayImageIndex(index);
  };

  const updateImageAlt = (index, alt) => {
    setSelectedImages((prev) =>
      prev.map((img, i) => (i === index ? { ...img, alt } : img))
    );
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);
    setError("");
    setSuccess("");

    try {
      const token = localStorage.getItem("adminToken");
      const formDataToSend = new FormData();

      // Add basic fields
      formDataToSend.append("slug", formData.slug);
      formDataToSend.append("whitelabelPrice", formData.whitelabelPrice);
      formDataToSend.append("subscriptionPrice", formData.subscriptionPrice);
      formDataToSend.append("demoUrl", formData.demoUrl);
      formDataToSend.append("status", formData.status);
      formDataToSend.append("featuredImageAlt", formData.featuredImageAlt);

      // Add arrays
      formDataToSend.append(
        "categoryIds",
        JSON.stringify(formData.categoryIds)
      );
      formDataToSend.append("tagIds", JSON.stringify(formData.tagIds));
      formDataToSend.append(
        "translations",
        JSON.stringify(formData.translations)
      );

      // Add images if selected
      let imageIndex = 0;
      selectedImages.forEach((imageData) => {
        // Only append new images (with file), existing images are handled separately
        if (imageData.file) {
          formDataToSend.append("images", imageData.file);
          formDataToSend.append(`imageAlt_${imageIndex}`, imageData.alt);
          formDataToSend.append(`isDisplay_${imageIndex}`, imageData.isDisplay);
          imageIndex++;
        }
      });

      // Send existing images data separately
      const existingImages = selectedImages
        .filter((img) => !img.file && img.id)
        .map((img) => ({
          id: img.id,
          alt: img.alt,
          isDisplay: img.isDisplay,
          sortOrder: img.sortOrder,
        }));

      if (existingImages.length > 0) {
        formDataToSend.append("existingImages", JSON.stringify(existingImages));
      }

      // Use the proper API utility
      let result;
      if (isEditing) {
        result = await adminAPI.updateProduct(id, formDataToSend);
      } else {
        result = await adminAPI.createProduct(formDataToSend);
      }

      const { response, data } = result;

      if (response.ok && data && data.success) {
        setSuccess(
          `Product ${isEditing ? "updated" : "created"} successfully!`
        );
        setTimeout(() => {
          navigate("/admin/products");
        }, 2000);
      } else {
        const errorMessage =
          data?.message ||
          `Failed to ${isEditing ? "update" : "create"} product`;
        setError(errorMessage);
      }
    } catch (error) {
      console.error("Error saving product:", error);
      setError(`Failed to ${isEditing ? "update" : "create"} product`);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <AdminLayout>
        <div
          className="d-flex justify-content-center align-items-center"
          style={{ minHeight: "400px" }}
        >
          <div className="text-center">
            <div className="spinner-border text-primary mb-3" role="status">
              <span className="visually-hidden">Loading...</span>
            </div>
            <p className="text-muted">Loading product data...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <>
      <SEO
        title={`${isEditing ? "Edit" : "Create"} Product | Admin`}
        description="Create and manage products in the webstore"
      />
      <AdminLayout title={isEditing ? "Edit Product" : "Create New Product"}>
        <form onSubmit={handleSubmit} className="admin-form">
          {/* Messages */}
          {error && (
            <div className="alert alert-danger mb-30" role="alert">
              <iconify-icon
                icon="solar:danger-triangle-bold"
                className="me-2"
              ></iconify-icon>
              {error}
            </div>
          )}

          {success && (
            <div className="alert alert-success mb-30" role="alert">
              <iconify-icon
                icon="solar:check-circle-bold"
                className="me-2"
              ></iconify-icon>
              {success}
            </div>
          )}

          {/* Basic Settings */}
          <div className="admin-table mb-40">
            <div className="row mb-30">
              <div className="col-12">
                <h3 className="hs-line-4 font-alt black mb-0">
                  <iconify-icon
                    icon="solar:settings-bold"
                    className="me-2 color-primary-1"
                  ></iconify-icon>
                  Basic Settings
                </h3>
                <p className="section-descr mb-0">
                  Configure the basic properties of your product
                </p>
              </div>
            </div>

            <div className="row">
              <div className="col-md-6 mb-30">
                <label className="form-label">
                  <iconify-icon
                    icon="solar:link-bold"
                    className="me-2"
                  ></iconify-icon>
                  Slug (URL)
                </label>
                <input
                  type="text"
                  value={formData.slug}
                  onChange={(e) => handleInputChange("slug", e.target.value)}
                  className="form-control"
                  placeholder="product-url-slug"
                />
                <small className="form-text text-muted">
                  This will be the URL path for your product (e.g.,
                  /webstore/your-slug)
                </small>
              </div>

              <div className="col-md-6 mb-30">
                <label className="form-label">
                  <iconify-icon
                    icon="solar:check-circle-bold"
                    className="me-2"
                  ></iconify-icon>
                  Publication Status
                </label>
                <select
                  value={formData.status}
                  onChange={(e) => handleInputChange("status", e.target.value)}
                  className="form-control"
                >
                  <option value="draft">Draft</option>
                  <option value="published">Published</option>
                </select>
                <small className="form-text text-muted">
                  Draft products are not visible to the public
                </small>
              </div>
            </div>
          </div>

          {/* Pricing Information */}
          <div className="admin-table mb-40">
            <div className="row mb-30">
              <div className="col-12">
                <h3 className="hs-line-4 font-alt black mb-0">
                  <iconify-icon
                    icon="solar:dollar-minimalistic-bold"
                    className="me-2 color-primary-1"
                  ></iconify-icon>
                  Pricing & Demo
                </h3>
                <p className="section-descr mb-0">
                  Set pricing options and demo URL for your product
                </p>
              </div>
            </div>

            <div className="row">
              <div className="col-md-4 mb-30">
                <label className="form-label">
                  <iconify-icon
                    icon="solar:code-bold"
                    className="me-2"
                  ></iconify-icon>
                  Whitelabel Price (EUR)
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={formData.whitelabelPrice}
                  onChange={(e) =>
                    handleInputChange("whitelabelPrice", e.target.value)
                  }
                  className="form-control"
                  placeholder="2999.99"
                />
                <small className="form-text text-muted">
                  Price for purchasing the source code with commercial license
                </small>
              </div>

              <div className="col-md-4 mb-30">
                <label className="form-label">
                  <iconify-icon
                    icon="solar:refresh-bold"
                    className="me-2"
                  ></iconify-icon>
                  Subscription Price (EUR/month)
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={formData.subscriptionPrice}
                  onChange={(e) =>
                    handleInputChange("subscriptionPrice", e.target.value)
                  }
                  className="form-control"
                  placeholder="99.99"
                />
                <small className="form-text text-muted">
                  Monthly subscription price for SaaS access
                </small>
              </div>

              <div className="col-md-4 mb-30">
                <label className="form-label">
                  <iconify-icon
                    icon="solar:eye-bold"
                    className="me-2"
                  ></iconify-icon>
                  Demo URL
                </label>
                <input
                  type="url"
                  value={formData.demoUrl}
                  onChange={(e) => handleInputChange("demoUrl", e.target.value)}
                  className="form-control"
                  placeholder="https://demo.example.com"
                />
                <small className="form-text text-muted">
                  Link to live demo of the product
                </small>
              </div>
            </div>
          </div>

          {/* Featured Image */}
          <div className="admin-table mb-40">
            <div className="row mb-30">
              <div className="col-12">
                <h3 className="hs-line-4 font-alt black mb-0">
                  <iconify-icon
                    icon="solar:gallery-bold"
                    className="me-2 color-primary-1"
                  ></iconify-icon>
                  Featured Image
                </h3>
                <p className="section-descr mb-0">
                  Upload a featured image that will be displayed with your
                  product
                </p>
              </div>
            </div>

            <div className="row">
              <div className="col-12 mb-30">
                <label className="form-label">
                  <iconify-icon
                    icon="solar:upload-bold"
                    className="me-2"
                  ></iconify-icon>
                  Upload Images (Max 10)
                </label>
                <input
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={handleImagesChange}
                  className="form-control"
                />
                <small className="form-text text-muted">
                  Recommended size: 800x600px. Max file size: 5MB per image.
                  Maximum 10 images.
                </small>
              </div>

              {selectedImages.length > 0 && (
                <div className="col-12 mb-30">
                  <h6 className="mb-3">
                    Product Images ({selectedImages.length}/10)
                  </h6>
                  <div className="row">
                    {selectedImages.map((imageData, index) => (
                      <div key={index} className="col-md-4 mb-3">
                        <div className="card">
                          <div className="position-relative">
                            <img
                              src={imageData.preview}
                              alt={`Preview ${index + 1}`}
                              className="card-img-top"
                              style={{ height: "200px", objectFit: "cover" }}
                              onError={(e) => {
                                console.error(
                                  "Failed to load image:",
                                  imageData.preview
                                );
                                e.target.style.display = "none";
                              }}
                              onLoad={() => {
                                console.log(
                                  "Successfully loaded image:",
                                  imageData.preview
                                );
                              }}
                            />
                            <button
                              type="button"
                              className="btn btn-danger btn-sm position-absolute top-0 end-0 m-2"
                              onClick={() => removeImage(index)}
                            >
                              ×
                            </button>
                            {imageData.isDisplay && (
                              <span className="badge bg-primary position-absolute top-0 start-0 m-2">
                                Display Image
                              </span>
                            )}
                          </div>
                          <div className="card-body">
                            <div className="mb-2">
                              <input
                                type="text"
                                className="form-control form-control-sm"
                                placeholder="Alt text"
                                value={imageData.alt}
                                onChange={(e) =>
                                  updateImageAlt(index, e.target.value)
                                }
                              />
                            </div>
                            <div className="d-flex gap-2">
                              {!imageData.isDisplay && (
                                <button
                                  type="button"
                                  className="btn btn-outline-primary btn-sm"
                                  onClick={() => setDisplayImage(index)}
                                >
                                  Set as Display
                                </button>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Language Tabs */}
          <div className="admin-table mb-40">
            <div className="row mb-30">
              <div className="col-12">
                <h3 className="hs-line-4 font-alt black mb-0">
                  <i className="mi-globe me-2 color-primary-1"></i>
                  Content (Multi-language)
                </h3>
                <p className="section-descr mb-0">
                  Create content in multiple languages. At least English content
                  is required.
                </p>
              </div>
            </div>

            {/* Language Selector */}
            <div className="language-tabs mb-30">
              {availableLanguages.map((lang) => (
                <button
                  key={lang}
                  type="button"
                  onClick={() => setActiveLanguage(lang)}
                  className={`language-tab ${
                    activeLanguage === lang ? "active" : ""
                  }`}
                >
                  <i className="mi-globe me-2"></i>
                  {lang.toUpperCase()}
                  {lang === "en" && (
                    <span className="ms-1 small">(Required)</span>
                  )}
                </button>
              ))}
            </div>

            {/* Content for Active Language */}
            <div className="row">
              <div className="col-12 mb-30">
                <label className="form-label">
                  <i className="mi-edit me-2"></i>
                  Title ({activeLanguage.toUpperCase()})
                  {activeLanguage === "en" && (
                    <span className="text-danger ms-1">*</span>
                  )}
                </label>
                <input
                  type="text"
                  value={formData.translations[activeLanguage]?.title || ""}
                  onChange={(e) =>
                    handleTranslationChange(
                      activeLanguage,
                      "title",
                      e.target.value
                    )
                  }
                  className="form-control"
                  placeholder="Enter product title"
                  required={activeLanguage === "en"}
                />
                <small className="form-text text-muted">
                  The main title of your product in{" "}
                  {activeLanguage.toUpperCase()}
                </small>
              </div>

              <div className="col-12 mb-30">
                <label className="form-label">
                  <i className="mi-text me-2"></i>
                  Excerpt ({activeLanguage.toUpperCase()})
                </label>
                <textarea
                  value={formData.translations[activeLanguage]?.excerpt || ""}
                  onChange={(e) =>
                    handleTranslationChange(
                      activeLanguage,
                      "excerpt",
                      e.target.value
                    )
                  }
                  rows={3}
                  className="form-control"
                  placeholder="Brief description of the product"
                />
                <small className="form-text text-muted">
                  A short summary that will appear in product listings and
                  social media previews
                </small>
              </div>

              <div className="col-12 mb-30">
                <label className="form-label">
                  <iconify-icon
                    icon="solar:document-text-bold"
                    className="me-2"
                  ></iconify-icon>
                  Content ({activeLanguage.toUpperCase()})
                  {activeLanguage === "en" && (
                    <span className="text-danger ms-1">*</span>
                  )}
                </label>
                <TipTapEditor
                  content={formData.translations[activeLanguage]?.content || ""}
                  onChange={(html) =>
                    handleTranslationChange(activeLanguage, "content", html)
                  }
                  placeholder="Write your product description here. You can paste formatted text and code snippets with syntax highlighting."
                />
                <small className="form-text text-muted">
                  <iconify-icon
                    icon="solar:info-circle-bold"
                    className="me-1"
                  ></iconify-icon>
                  Rich text editor with syntax highlighting. Paste code snippets
                  and they will be automatically highlighted. Use the toolbar
                  for formatting options.
                </small>
              </div>

              <div className="col-md-6 mb-30">
                <label className="form-label">
                  <i className="mi-seo me-2"></i>
                  Meta Title ({activeLanguage.toUpperCase()})
                </label>
                <input
                  type="text"
                  value={formData.translations[activeLanguage]?.metaTitle || ""}
                  onChange={(e) =>
                    handleTranslationChange(
                      activeLanguage,
                      "metaTitle",
                      e.target.value
                    )
                  }
                  className="form-control"
                  placeholder="SEO title (optional)"
                  maxLength="60"
                />
                <small className="form-text text-muted">
                  SEO title for search engines (max 60 characters)
                </small>
              </div>

              <div className="col-md-6 mb-30">
                <label className="form-label">
                  <i className="mi-seo me-2"></i>
                  Meta Description ({activeLanguage.toUpperCase()})
                </label>
                <textarea
                  value={formData.translations[activeLanguage]?.metaDesc || ""}
                  onChange={(e) =>
                    handleTranslationChange(
                      activeLanguage,
                      "metaDesc",
                      e.target.value
                    )
                  }
                  rows={3}
                  className="form-control"
                  placeholder="SEO description (optional)"
                  maxLength="160"
                />
                <small className="form-text text-muted">
                  SEO description for search engines (max 160 characters)
                </small>
              </div>
            </div>
          </div>

          {/* Categories and Tags */}
          <div className="admin-table mb-40">
            <div className="row mb-30">
              <div className="col-12">
                <h3 className="hs-line-4 font-alt black mb-0">
                  <iconify-icon
                    icon="solar:tag-bold"
                    className="me-2 color-primary-1"
                  ></iconify-icon>
                  Categories & Tags
                </h3>
                <p className="section-descr mb-0">
                  Organize your product with categories and tags
                </p>
              </div>
            </div>

            <div className="row">
              <div className="col-md-6 mb-30">
                <label className="form-label">
                  <iconify-icon
                    icon="solar:folder-bold"
                    className="me-2"
                  ></iconify-icon>
                  Categories
                </label>
                <div className="categories-grid">
                  {categories && categories.length > 0 ? (
                    categories.map((category) => (
                      <div key={category.id} className="form-check mb-2">
                        <input
                          className="form-check-input"
                          type="checkbox"
                          id={`category-${category.id}`}
                          checked={formData.categoryIds.includes(category.id)}
                          onChange={() => {
                            const newCategoryIds =
                              formData.categoryIds.includes(category.id)
                                ? formData.categoryIds.filter(
                                    (id) => id !== category.id
                                  )
                                : [...formData.categoryIds, category.id];
                            handleInputChange("categoryIds", newCategoryIds);
                          }}
                        />
                        <label
                          className="form-check-label"
                          htmlFor={`category-${category.id}`}
                        >
                          {category.name}
                        </label>
                      </div>
                    ))
                  ) : (
                    <p className="text-muted">No categories available</p>
                  )}
                </div>
              </div>

              <div className="col-md-6 mb-30">
                <label className="form-label">
                  <iconify-icon
                    icon="solar:hashtag-bold"
                    className="me-2"
                  ></iconify-icon>
                  Tags
                </label>
                <div className="tags-grid">
                  {tags && tags.length > 0 ? (
                    tags.map((tag) => (
                      <div key={tag.id} className="form-check mb-2">
                        <input
                          className="form-check-input"
                          type="checkbox"
                          id={`tag-${tag.id}`}
                          checked={formData.tagIds.includes(tag.id)}
                          onChange={() => {
                            const newTagIds = formData.tagIds.includes(tag.id)
                              ? formData.tagIds.filter((id) => id !== tag.id)
                              : [...formData.tagIds, tag.id];
                            handleInputChange("tagIds", newTagIds);
                          }}
                        />
                        <label
                          className="form-check-label"
                          htmlFor={`tag-${tag.id}`}
                        >
                          {tag.name}
                        </label>
                      </div>
                    ))
                  ) : (
                    <p className="text-muted">No tags available</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="row">
            <div className="col-12 text-center">
              <button
                type="submit"
                className="btn btn-mod btn-color btn-large btn-round"
                disabled={saving}
              >
                {saving ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2"></span>
                    {isEditing ? "Updating..." : "Creating..."}
                  </>
                ) : (
                  <>
                    <i className="mi-check me-2"></i>
                    {isEditing ? "Update Product" : "Create Product"}
                  </>
                )}
              </button>
              <div className="mt-3">
                <small className="text-muted">
                  <i className="mi-info me-1"></i>
                  {isEditing
                    ? "Changes will be saved and the product will be updated"
                    : "The product will be created and added to your webstore"}
                </small>
              </div>
            </div>
          </div>
        </form>
      </AdminLayout>
    </>
  );
};

export default AdminProductEditor;
