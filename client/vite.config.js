/* eslint-disable no-undef */
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    open: true, // Automatically open the browser
    proxy: {
      "/api": {
        target: "http://localhost:4004",
        changeOrigin: true,
        secure: false,
      },
    },
  },
  build: {
    outDir: "build",
    // Generate source maps for better debugging
    sourcemap: true,
    // Use default minification to avoid issues
    minify: true,
    // Increase chunk size warning limit to 1000kb
    chunkSizeWarningLimit: 1000,
    // Copy files from public directory to build output
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Vendor chunk for core React libraries
          if (id.includes("node_modules")) {
            if (
              id.includes("react") ||
              id.includes("react-dom") ||
              id.includes("react-router")
            ) {
              return "vendor-react";
            }

            // ADMIN-ONLY LIBRARIES - Separate chunk (will be lazy loaded)
            if (
              id.includes("chart.js") ||
              id.includes("react-chartjs-2") ||
              id.includes("@tiptap") ||
              id.includes("prosemirror") ||
              id.includes("lowlight")
            ) {
              return "vendor-admin";
            }

            // GALLERY LIBRARIES - Separate chunk (lazy loaded when needed)
            if (
              id.includes("photoswipe") ||
              id.includes("react-photoswipe-gallery")
            ) {
              return "vendor-gallery";
            }

            // UI libraries chunk (essential for public pages)
            if (id.includes("bootstrap") || id.includes("swiper")) {
              return "vendor-ui";
            }

            // Animation libraries chunk (essential for public pages)
            if (
              id.includes("wow") ||
              id.includes("rellax") ||
              id.includes("jarallax")
            ) {
              return "vendor-animations";
            }

            // i18n libraries (essential for public pages)
            if (id.includes("i18next") || id.includes("react-i18next")) {
              return "vendor-i18n";
            }

            // Utility libraries chunk (essential)
            if (
              id.includes("axios") ||
              id.includes("zustand") ||
              id.includes("helmet")
            ) {
              return "vendor-utils";
            }

            // Exclude Shiki from vendor chunks - it will be lazy loaded
            if (id.includes("shiki") || id.includes("@shikijs")) {
              return undefined; // Let Vite handle it as dynamic imports
            }

            // All other vendor libraries (should be minimal now)
            return "vendor-misc";
          }

          // Split pages into separate chunks
          if (id.includes("/pages/")) {
            if (id.includes("/products/")) {
              return "pages-products";
            }
            if (
              id.includes("/about/") ||
              id.includes("/contact/") ||
              id.includes("/services/")
            ) {
              return "pages-static";
            }
            return "pages-other";
          }

          // Split components by type
          if (id.includes("/components/")) {
            if (id.includes("/headers/") || id.includes("/footers/")) {
              return "components-layout";
            }
            if (id.includes("/home/")) {
              return "components-home";
            }
            return "components-common";
          }
        },
      },
    },
  },
});
