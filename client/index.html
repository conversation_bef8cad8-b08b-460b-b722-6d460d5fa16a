<!DOCTYPE html>
<html lang="en" class="no-mobile no-touch">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon-theme.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#06B6D4" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta
      name="description"
      content="DevSkills.ee offers a comprehensive Business Management System (BMS) to streamline your operations and boost productivity."
    />
    <meta name="robots" content="index, follow" />
    <link rel="manifest" href="/manifest.json" />
    <script type="application/ld+json" src="/structured-data.json"></script>
    <title>DevSkills.ee - Business Management System</title>

    <!-- Force clear old service worker cache -->
    <script>
      if ("serviceWorker" in navigator) {
        navigator.serviceWorker
          .getRegistrations()
          .then(function (registrations) {
            for (let registration of registrations) {
              registration.unregister();
            }
          });
      }
      // Clear all caches
      if ("caches" in window) {
        caches.keys().then(function (names) {
          for (let name of names) {
            caches.delete(name);
          }
        });
      }
    </script>

    <!-- Google Analytics 4 with Consent Mode -->
    <script>
      // Initialize dataLayer and gtag function
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }

      // Set default consent mode
      gtag("consent", "default", {
        analytics_storage: "denied",
        ad_storage: "denied",
        ad_user_data: "denied",
        ad_personalization: "denied",
        wait_for_update: 500,
      });

      gtag("js", new Date());
      gtag("config", "G-8NEGL4LL8Q", {
        anonymize_ip: true,
        allow_google_signals: false,
        allow_ad_personalization_signals: false,
      });
    </script>
    <script
      async
      src="https://www.googletagmanager.com/gtag/js?id=G-8NEGL4LL8Q"
    ></script>

    <link
      href="https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,400;0,500;1,400;1,500&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Epilogue:wght@400;500&family=Poppins&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,400;0,500;0,600;1,400&display=swap"
      rel="stylesheet"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,300;0,400;0,500;0,600;1,400&display=swap"
      rel="stylesheet"
    />

    <!-- Critical GDPR Banner CSS - Inlined for LCP Performance -->
    <style>
      /* Critical GDPR Banner Styles - Prevent render blocking */
      .gdpr-banner {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(40, 40, 40, 0.95);
        backdrop-filter: blur(20px);
        color: #ffffff;
        padding: 20px 0;
        box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
        z-index: 9999;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        transform: translateZ(0);
        will-change: transform;
        contain: layout style paint;
        display: none; /* Hidden by default, shown by JS if needed */
      }
      .gdpr-banner.show {
        display: block;
      }
      .gdpr-content {
        text-align: left;
        padding-right: 20px;
      }
      .gdpr-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #ffffff;
        margin-bottom: 8px;
      }
      .gdpr-description {
        font-size: 0.9rem;
        line-height: 1.4;
        color: #e0e0e0;
        margin-bottom: 0;
      }
      .gdpr-buttons {
        display: flex;
        gap: 10px;
        flex-wrap: nowrap;
        justify-content: flex-end;
        align-items: center;
      }
      .gdpr-buttons .btn {
        white-space: nowrap;
      }
      /* Mobile responsive */
      @media (max-width: 768px) {
        .gdpr-banner {
          padding: 15px 0;
        }
        .gdpr-content {
          padding-right: 10px;
          margin-bottom: 15px;
        }
        .gdpr-title {
          font-size: 1rem;
        }
        .gdpr-description {
          font-size: 0.8rem;
        }
        .gdpr-buttons {
          gap: 6px;
          justify-content: center;
          flex-wrap: wrap;
        }
        .gdpr-buttons .btn {
          font-size: 0.75rem;
          padding: 6px 10px;
          min-width: auto;
        }
      }
    </style>

    <!-- Iconify Web Component for Solar Icons - Deferred for LCP performance -->
    <script
      defer
      src="https://code.iconify.design/iconify-icon/2.1.0/iconify-icon.min.js"
    ></script>
  </head>
  <body class="appear-animate body">
    <div id="root"></div>

    <!-- Server-side rendered GDPR banner for immediate LCP -->
    <div id="gdpr-banner-ssr" class="gdpr-banner">
      <div class="container">
        <div class="row align-items-center">
          <div class="col-lg-8 col-md-7">
            <div class="gdpr-content">
              <h6 class="gdpr-title mb-2">Cookie Notice</h6>
              <p class="gdpr-description mb-0">
                We use cookies and similar technologies to improve your browsing
                experience and analyze site traffic. By clicking "Accept All",
                you consent to our use of cookies.
              </p>
            </div>
          </div>
          <div class="col-lg-4 col-md-5">
            <div class="gdpr-buttons">
              <button
                class="btn btn-mod btn-small btn-w btn-circle gdpr-btn-accept"
                onclick="window.gdprAcceptAll()"
              >
                Accept All
              </button>
              <button
                class="btn btn-mod btn-small btn-border-w btn-circle"
                onclick="window.gdprRejectAll()"
              >
                Reject All
              </button>
              <button
                class="btn btn-mod btn-small btn-border-w btn-circle"
                onclick="window.gdprCustomize()"
              >
                Customize
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Immediate GDPR consent check script -->
    <script>
      // Immediately check for existing consent and hide banner if found
      (function () {
        const consent = localStorage.getItem("gdpr-consent");
        const banner = document.getElementById("gdpr-banner-ssr");

        if (consent) {
          // User already gave consent, hide banner
          banner.style.display = "none";
        } else {
          // No consent found, show banner
          banner.classList.add("show");
        }

        // Global functions for banner buttons (will be replaced by React)
        window.gdprAcceptAll = function () {
          const consent = {
            necessary: true,
            analytics: true,
            marketing: true,
            timestamp: new Date().toISOString(),
          };
          localStorage.setItem("gdpr-consent", JSON.stringify(consent));
          banner.style.display = "none";

          // Initialize Google Analytics
          if (window.gtag) {
            gtag("consent", "update", {
              analytics_storage: "granted",
              ad_storage: "granted",
              ad_user_data: "granted",
              ad_personalization: "granted",
            });
          }
        };

        window.gdprRejectAll = function () {
          const consent = {
            necessary: true,
            analytics: false,
            marketing: false,
            timestamp: new Date().toISOString(),
          };
          localStorage.setItem("gdpr-consent", JSON.stringify(consent));
          banner.style.display = "none";
        };

        window.gdprCustomize = function () {
          // This will be handled by React component when it loads
          console.log("Customize clicked - React component will handle this");
        };
      })();
    </script>

    <script type="module" src="/src/main.jsx"></script>
    <script src="/scripts/simple-slider.js"></script>
  </body>
</html>
