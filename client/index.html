<!DOCTYPE html>
<html lang="en" class="no-mobile no-touch">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon-theme.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="theme-color" content="#06B6D4" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta
      name="description"
      content="DevSkills.ee offers a comprehensive Business Management System (BMS) to streamline your operations and boost productivity."
    />
    <meta name="robots" content="index, follow" />
    <link rel="manifest" href="/manifest.json" />
    <script type="application/ld+json" src="/structured-data.json"></script>
    <title>DevSkills.ee - Business Management System</title>

    <!-- Force clear old service worker cache -->
    <script>
      if ("serviceWorker" in navigator) {
        navigator.serviceWorker
          .getRegistrations()
          .then(function (registrations) {
            for (let registration of registrations) {
              registration.unregister();
            }
          });
      }
      // Clear all caches
      if ("caches" in window) {
        caches.keys().then(function (names) {
          for (let name of names) {
            caches.delete(name);
          }
        });
      }
    </script>

    <!-- Google Analytics 4 with Consent Mode -->
    <script>
      // Initialize dataLayer and gtag function
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }

      // Set default consent mode
      gtag("consent", "default", {
        analytics_storage: "denied",
        ad_storage: "denied",
        ad_user_data: "denied",
        ad_personalization: "denied",
        wait_for_update: 500,
      });

      gtag("js", new Date());
      gtag("config", "G-8NEGL4LL8Q", {
        anonymize_ip: true,
        allow_google_signals: false,
        allow_ad_personalization_signals: false,
      });
    </script>
    <script
      async
      src="https://www.googletagmanager.com/gtag/js?id=G-8NEGL4LL8Q"
    ></script>

    <!-- Preload critical font -->
    <link
      rel="preload"
      href="https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap"
      as="style"
      onload="this.onload=null;this.rel='stylesheet'"
    />
    <noscript
      ><link
        href="https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap"
        rel="stylesheet"
    /></noscript>

    <!-- Load other fonts asynchronously -->
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,400;0,500;1,400;1,500&display=swap"
      rel="stylesheet"
      media="print"
      onload="this.media='all'"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Epilogue:wght@400;500&family=Poppins&display=swap"
      rel="stylesheet"
      media="print"
      onload="this.media='all'"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
      rel="stylesheet"
      media="print"
      onload="this.media='all'"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,400;0,500;0,600;1,400&display=swap"
      rel="stylesheet"
      media="print"
      onload="this.media='all'"
    />
    <link
      href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,300;0,400;0,500;0,600;1,400&display=swap"
      rel="stylesheet"
      media="print"
      onload="this.media='all'"
    />

    <!-- Iconify Web Component for Solar Icons -->
    <script src="https://code.iconify.design/iconify-icon/2.1.0/iconify-icon.min.js"></script>

    <!-- CRITICAL CSS - Inlined for immediate loading -->
    <style>
      /* CRITICAL CSS - Only essential styles for above-the-fold content */

      /* Essential CSS Variables - Critical for All Styles */
      :root {
        --font-global: "DM Sans", sans-serif;
        --font-family-primary: "DM Sans", sans-serif;
        --container-width: 1350px;
        --section-padding-y: 120px;
        --color-dark-1: #010101;
        --color-primary-1: #4567ed;
        --color-primary-1-a: #375ae3;
        --color-primary-light-1: #e3effe;
        --color-gray-1: #777;
        --transition-default: all 0.27s cubic-bezier(0, 0, 0.58, 1);
        --ease-default: cubic-bezier(0, 0, 0.58, 1);
        --ease-out-short: cubic-bezier(0.15, 0.7, 0.78, 1);
        --ease-elastic-1: cubic-bezier(0.68, -0.55, 0.27, 1.55);
        --ease-elastic-2: cubic-bezier(0.68, -3, 0.27, 5);
        --full-wrapper-margin-x: 30px;
        --menu-bar-height: 85px;
        --menu-bar-height-scrolled: 65px;
        --border-radius-default: 4px;
        --border-radius-large: 30px;
      }

      /* Dark mode overrides */
      .theme-elegant .dark-mode {
        --color-dark-1: #171717;
        --color-dark-2: #222;
      }

      /* Essential Typography */
      body {
        font-family: var(--font-global), -apple-system, BlinkMacSystemFont,
          "Segoe UI", Roboto, sans-serif;
        font-size: 18px;
        line-height: 1.67;
        color: #fff;
        background-color: #000;
        margin: 0;
        padding: 0;
        overflow-x: hidden;
        font-display: swap;
      }

      /* Critical Layout Classes */
      .page {
        position: relative;
        overflow: hidden;
      }

      .bg-dark-1 {
        background-color: var(--color-dark-1) !important;
      }

      .theme-elegant {
        color: var(--color-dark-1);
        font-family: var(--font-global);
      }

      /* Essential Layout Classes */
      .container {
        position: relative;
        width: 100%;
        max-width: var(--container-width);
        margin: 0 auto;
        padding: 0 var(--full-wrapper-margin-x);
      }

      .min-height-100vh {
        min-height: 100vh;
      }

      .d-flex {
        display: flex !important;
      }

      .align-items-center {
        align-items: center !important;
      }

      .text-center {
        text-align: center !important;
      }

      .position-relative {
        position: relative !important;
      }

      .z-index-1 {
        z-index: 1 !important;
      }

      .light-content {
        color: #fff;
      }

      .bg-dark-alpha-30:before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.3);
        z-index: -1;
      }

      .parallax-5 {
        background-attachment: fixed;
        background-position: center;
        background-repeat: no-repeat;
        background-size: cover;
      }

      /* Essential Jarallax Styles for Parallax */
      .jarallax {
        position: relative;
        z-index: 0;
      }

      .jarallax > .jarallax-img {
        position: absolute;
        object-fit: cover;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
      }

      /* Essential Scroll Down Styles */
      .scroll-down-3-wrap {
        position: absolute;
        bottom: 50px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 10;
      }

      .scroll-down-3 {
        display: block;
        color: #fff;
        text-decoration: none;
        font-size: 12px;
        text-transform: uppercase;
        letter-spacing: 0.1em;
        opacity: 0.7;
        transition: opacity 0.3s ease;
      }

      .scroll-down-3:hover {
        opacity: 1;
        color: #fff;
      }

      .home-section {
        position: relative;
        min-height: 100vh;
        display: flex;
        align-items: center;
      }

      .home-content {
        width: 100%;
      }

      /* Essential Typography Classes */
      .hs-title-3 {
        margin: 0;
        font-size: 3.5rem;
        font-weight: 400;
        line-height: 1.1;
        letter-spacing: -0.02em;
        color: #fff;
      }

      .section-title-tiny {
        margin: 0;
        font-size: 14px;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.1em;
        color: var(--color-gray-1);
      }

      /* Essential Padding/Margin Classes */
      .pt-100 {
        padding-top: 100px;
      }
      .pb-100 {
        padding-bottom: 100px;
      }
      .pt-sm-120 {
        padding-top: 120px;
      }
      .pb-sm-120 {
        padding-bottom: 120px;
      }

      /* Essential Button Styles */
      .btn-mod {
        display: inline-block;
        padding: 12px 30px;
        font-size: 14px;
        font-weight: 500;
        text-align: center;
        text-decoration: none;
        border: 1px solid transparent;
        border-radius: var(--border-radius-default);
        background: transparent;
        color: #fff;
        cursor: pointer;
        transition: var(--transition-default);
      }

      .btn-mod.btn-border-w {
        color: #fff;
        border: 1px solid #fff;
        background: transparent;
      }

      .btn-mod.btn-border-w:hover {
        color: var(--color-dark-1);
        background: #fff;
      }

      .btn-mod.btn-small {
        padding: 8px 20px;
        font-size: 12px;
      }

      .btn-mod.btn-circle {
        border-radius: 30px;
      }

      /* Button Animation */
      .btn-animate-y {
        position: relative;
        overflow: hidden;
      }

      .btn-animate-y-1 {
        display: block;
        transition: all 0.37s var(--ease-out-short), opacity 0.37s linear;
      }

      .btn-animate-y-2 {
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        opacity: 0;
        transform: translate(0, 100%);
        transition: all 0.37s var(--ease-out-short), opacity 0.37s linear;
      }

      .btn-mod:hover .btn-animate-y-1 {
        opacity: 0;
        transform: translate(0, -100%);
      }

      .btn-mod:hover .btn-animate-y-2 {
        opacity: 1;
        transform: translate(0, 0);
      }

      /* Essential Link Animations */
      .link-hover-anim {
        position: relative;
        display: inline-block;
        text-decoration: none;
        color: inherit;
      }

      .link-strong {
        position: relative;
        display: block;
        overflow: hidden;
        padding: 7px 0;
        transition: transform 0.5s var(--ease-elastic-1),
          opacity 0.5s var(--ease-elastic-1), color 0.2s var(--ease-default);
      }

      .link-strong-unhovered {
        position: relative;
        display: block;
        transition: all 0.37s var(--ease-out-short);
      }

      .link-strong-hovered {
        position: absolute;
        top: 100%;
        left: 0;
        display: block;
        opacity: 0;
        transition: all 0.37s var(--ease-out-short);
      }

      .link-hover-anim:hover .link-strong-unhovered {
        transform: translate(0, -100%);
      }

      .link-hover-anim:hover .link-strong-hovered {
        opacity: 1;
        transform: translate(0, -100%);
      }

      .link-circle-1 {
        margin-left: -7px;
        padding-left: 27px;
      }

      .link-circle-1:before {
        content: "";
        position: absolute;
        top: 50%;
        left: 7px;
        transform: translate3d(0, -50%, 0.001px);
        display: block;
        width: 4px;
        height: 4px;
        border: 1px solid currentColor;
        border-radius: 50%;
        opacity: 0.15;
        transition: all 0.5s var(--ease-elastic-2);
      }

      .link-circle-1:hover:before {
        transform: translate3d(0, -50%, 0.001px) scale(0.88);
      }

      /* Essential Character Animations */
      .charsAnimIn .char {
        opacity: 0;
        transform: translateY(0.5em) rotate(7deg);
        transform-origin: 0 50%;
        transition: transform 0.62s var(--ease-out-short),
          opacity 0.62s var(--ease-default);
        transition-delay: calc(0.027s * var(--char-index));
      }

      .charsAnimInLong .char {
        opacity: 0;
        transform: translateX(0.5em);
        transform-origin: 0 50%;
        transition: transform 0.85s var(--ease-out-short),
          opacity 0.62s var(--ease-default);
        transition-delay: calc(0.03s * var(--char-index));
      }

      .appear-animate .charsAnimIn .char,
      .appear-animate .charsAnimInLong .char {
        opacity: 1;
        transform: none;
      }

      /* Essential Splitting Classes */
      .splitting {
        --word-center: calc((var(--word-total) - 1) / 2);
        --char-center: calc((var(--char-total) - 1) / 2);
      }

      .splitting .word {
        display: inline-block;
      }

      .splitting .char {
        display: inline-block;
      }

      /* Essential Navigation */
      .main-nav {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: var(--menu-bar-height);
        z-index: 1030;
        transition: all 0.2s var(--ease-default);
      }

      .main-nav.dark {
        background: rgba(0, 0, 0, 0.9);
      }

      .main-nav.transparent {
        background: transparent;
      }

      .main-nav-sub {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        height: 100%;
        padding: 0 var(--full-wrapper-margin-x);
      }

      .full-wrapper {
        width: 100%;
      }

      .nav-logo-wrap {
        display: flex;
        align-items: center;
      }

      .logo {
        display: flex;
        align-items: center;
        text-decoration: none;
        color: #fff;
        font-weight: 500;
        font-size: 18px;
      }

      .font-alt {
        font-family: var(--font-global);
      }

      .gap-2 {
        gap: 0.5rem;
      }

      .mt-1 {
        margin-top: 0.25rem;
      }

      .pointer-event {
        pointer-events: auto;
      }

      /* Essential Icon Styles */
      .mi-arrow-right {
        display: inline-block;
        font-style: normal;
      }

      .mi-arrow-right:before {
        content: "→";
      }

      .size-18 {
        font-size: 18px;
      }

      .align-middle {
        vertical-align: middle;
      }

      /* Essential Footer */
      .footer {
        position: relative;
        background: var(--color-dark-2);
        color: #fff;
      }

      .bg-dark-2 {
        background-color: var(--color-dark-2) !important;
      }

      /* Essential Margin/Padding Utilities */
      .mb-50 {
        margin-bottom: 50px;
      }
      .mb-sm-30 {
        margin-bottom: 30px;
      }
      .mb-120 {
        margin-bottom: 120px;
      }
      .mb-sm-80 {
        margin-bottom: 80px;
      }
      .mb-xs-140 {
        margin-bottom: 140px;
      }

      /* Essential WOW Animation Classes */
      .wow {
        visibility: hidden;
      }

      .wow.animated {
        visibility: visible;
      }

      .fadeInDownShort {
        animation-name: fadeInDownShort;
        animation-duration: 0.6s;
        animation-fill-mode: both;
      }

      .fadeInUpShort {
        animation-name: fadeInUpShort;
        animation-duration: 0.6s;
        animation-fill-mode: both;
      }

      .stick-fixed {
        position: fixed !important;
      }

      @keyframes fadeInDownShort {
        0% {
          opacity: 0;
          transform: translateY(-20px);
        }
        100% {
          opacity: 1;
          transform: translateY(0);
        }
      }

      @keyframes fadeInUpShort {
        0% {
          opacity: 0;
          transform: translateY(20px);
        }
        100% {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* Prevent FOUC */
      .appear-animate {
        opacity: 1;
      }

      /* Essential responsive breakpoints */
      @media (max-width: 767px) {
        .hs-title-3 {
          font-size: 2.5rem;
        }
        .mb-sm-30 {
          margin-bottom: 30px;
        }
        .mb-sm-80 {
          margin-bottom: 80px;
        }
        .pt-sm-120 {
          padding-top: 120px;
        }
        .pb-sm-120 {
          padding-bottom: 120px;
        }
      }

      @media (max-width: 479px) {
        .hs-title-3 {
          font-size: 2rem;
        }
        .mb-xs-140 {
          margin-bottom: 140px;
        }
      }

      /* Ensure fonts load properly */
      * {
        font-family: var(--font-global), -apple-system, BlinkMacSystemFont,
          "Segoe UI", Roboto, sans-serif;
      }

      /* Prevent invisible text during font swap */
      .hs-title-3,
      .section-title-tiny,
      .logo {
        font-display: swap;
      }
    </style>
  </head>
  <body class="appear-animate body">
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
    <script src="/scripts/simple-slider.js"></script>
  </body>
</html>
