name: Deploy to Server
on:
  push:
    branches:
      - main
jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Create .ssh directory
        run: mkdir -p ~/.ssh
      - name: Add server to known hosts
        run: ssh-keyscan -H ************ >> ~/.ssh/known_hosts
      - name: Deploy to server
        env:
          SSH_AUTH_SOCK: /tmp/ssh_agent.sock
        run: |
          echo "${{ secrets.SSH_PRIVATE_KEY }}" > private_key
          chmod 600 private_key
          eval "$(ssh-agent -s)"
          ssh-add private_key
          ssh -o StrictHostKeyChecking=no timo@************ << 'EOF'
            cd /home/<USER>/Projects/devskills
            echo "📁 Current directory: $(pwd)"
            echo "🔄 Updating code..."
            export GIT_SSH_COMMAND="ssh -i ~/.ssh/id_ed25519_devskills -F /dev/null"
            git fetch origin && git reset --hard origin/main || echo "⚠️ Git update failed, using existing code"
            echo "📝 Current commit: $(git log --oneline -1)"
            echo "🚀 Starting deployment..."
            bash ./deploy.sh
          EOF
          rm private_key
