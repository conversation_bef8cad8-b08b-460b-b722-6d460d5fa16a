import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Create admin user
  const hashedPassword = await bcrypt.hash('Valgehunt#1405', 12);
  
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      name: '<PERSON><PERSON>',
      role: 'ADMIN',
    },
  });

  console.log('✅ Admin user created:', adminUser.email);

  // Create some sample categories
  const categories = [
    {
      slug: 'web-development',
      name: 'Web Development',
      description: 'Articles about web development, frameworks, and best practices',
      color: '#3B82F6',
    },
    {
      slug: 'mobile-development',
      name: 'Mobile Development',
      description: 'Mobile app development guides and tutorials',
      color: '#10B981',
    },
    {
      slug: 'ai-machine-learning',
      name: 'AI & Machine Learning',
      description: 'Artificial intelligence and machine learning content',
      color: '#8B5CF6',
    },
    {
      slug: 'business-management',
      name: 'Business Management',
      description: 'Business management systems and strategies',
      color: '#F59E0B',
    },
    {
      slug: 'technology-trends',
      name: 'Technology Trends',
      description: 'Latest trends in technology and software development',
      color: '#EF4444',
    },
  ];

  for (const category of categories) {
    await prisma.category.upsert({
      where: { slug: category.slug },
      update: {},
      create: category,
    });
  }

  console.log('✅ Sample categories created');

  // Create some sample tags
  const tags = [
    { slug: 'react', name: 'React' },
    { slug: 'typescript', name: 'TypeScript' },
    { slug: 'nodejs', name: 'Node.js' },
    { slug: 'prisma', name: 'Prisma' },
    { slug: 'nextjs', name: 'Next.js' },
    { slug: 'javascript', name: 'JavaScript' },
    { slug: 'css', name: 'CSS' },
    { slug: 'html', name: 'HTML' },
    { slug: 'api', name: 'API' },
    { slug: 'database', name: 'Database' },
    { slug: 'seo', name: 'SEO' },
    { slug: 'performance', name: 'Performance' },
    { slug: 'security', name: 'Security' },
    { slug: 'testing', name: 'Testing' },
    { slug: 'deployment', name: 'Deployment' },
  ];

  for (const tag of tags) {
    await prisma.tag.upsert({
      where: { slug: tag.slug },
      update: {},
      create: tag,
    });
  }

  console.log('✅ Sample tags created');

  console.log('🎉 Database seed completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Seed failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
