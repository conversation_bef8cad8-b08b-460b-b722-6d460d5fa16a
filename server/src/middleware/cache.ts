import { Request, Response, NextFunction } from 'express';
import { CacheService } from '../config/redis';

// Simple cache middleware for GET requests
export const apiCache = (ttlSeconds: number = 3600) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    // Only cache GET requests
    if (req.method !== 'GET') {
      return next();
    }

    // Create cache key from URL and query parameters
    const cacheKey = `api:${req.originalUrl}`;

    try {
      // Try to get cached response
      const cachedResponse = await CacheService.get(cacheKey);
      
      if (cachedResponse) {
        console.log(`🎯 Cache HIT: ${cacheKey}`);
        res.set('X-Cache', 'HIT');
        return res.json(cachedResponse);
      }

      console.log(`❌ Cache MISS: ${cacheKey}`);
      
      // Store original json method
      const originalJson = res.json;
      
      // Override json method to cache response
      res.json = function(data: any) {
        // Cache successful responses
        if (res.statusCode >= 200 && res.statusCode < 300) {
          CacheService.set(cacheKey, data, ttlSeconds).catch(error => {
            console.error('Failed to cache response:', error);
          });
        }
        
        res.set('X-Cache', 'MISS');
        return originalJson.call(this, data);
      };
      
      next();
    } catch (error) {
      console.error('Cache middleware error:', error);
      next();
    }
  };
};

// Cache invalidation helper
export const invalidateCache = async (pattern: string) => {
  // For now, just delete specific keys
  // In a full implementation, we'd use Redis SCAN for pattern matching
  await CacheService.del(pattern);
};
