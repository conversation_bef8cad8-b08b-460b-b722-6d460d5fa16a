import express from "express";
import rateLimit from "express-rate-limit";
import { sendContactForm } from "../controllers/contactController";

const router = express.Router();

// Rate limiting for contact form - disabled in development
const contactLimiter =
  process.env.NODE_ENV === "production"
    ? rateLimit({
        windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || "900000"), // 15 minutes
        max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || "5"), // 5 requests per window
        message: {
          success: false,
          message: "Too many contact form submissions, please try again later.",
        },
        standardHeaders: true,
        legacyHeaders: false,
      })
    : (
        req: express.Request,
        res: express.Response,
        next: express.NextFunction
      ) => next(); // No-op in development

// API Key middleware for contact form
const validateApiKey = (
  req: express.Request,
  res: express.Response,
  next: express.NextFunction
) => {
  const apiKey = req.headers["x-api-key"];
  if (!apiKey || apiKey !== process.env.API_KEY) {
    return res.status(401).json({
      success: false,
      message: "Invalid or missing API key",
    });
  }
  next();
};

// @route   POST /api/contact
router.post("/", contactLimiter, validateApiKey, sendContactForm);

// Legacy route for backward compatibility
router.post(
  "/v1/communication/public/contact-form",
  contactLimiter,
  validateApiKey,
  sendContactForm
);

export default router;
