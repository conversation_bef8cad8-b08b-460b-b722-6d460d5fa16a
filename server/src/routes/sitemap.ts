import express from "express";
import {
  getSitemapIndex,
  getStaticSitemap,
  getBlogSitemap,
  getProductsSitemap,
  getLanguageSitemap,
  getDiscoveredRoutesDebug,
} from "../controllers/sitemapController";

const router = express.Router();

// Main sitemap index
router.get("/sitemap.xml", getSitemapIndex);

// Static pages sitemap
router.get("/sitemap-static.xml", getStaticSitemap);

// Blog posts sitemap
router.get("/sitemap-blog.xml", getBlogSitemap);

// Products sitemap
router.get("/sitemap-products.xml", getProductsSitemap);

// Language-specific sitemaps
router.get("/sitemap-:lang.xml", getLanguageSitemap);

// Debug endpoint to see discovered routes (development only)
if (process.env.NODE_ENV === "development") {
  router.get("/debug/routes", getDiscoveredRoutesDebug);
}

export default router;
