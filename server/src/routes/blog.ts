import express from "express";
import {
  getBlogPosts,
  getBlogPost,
  getBlogArchive,
  createBlogPost,
  updateBlogPost,
  deleteBlogPost,
  toggleBlogPostVisibility,
  createComment,
} from "../controllers/blogController";
import { authenticate, authorize } from "../middleware/auth";
import { uploadBlogImage } from "../middleware/upload";
import { apiCache } from "../middleware/cache";

const router = express.Router();

// Public routes with caching (1 hour cache)
router.get("/", apiCache(3600), getBlogPosts);
router.get("/archive", apiCache(3600), getBlogArchive);
router.get("/:slug", apiCache(3600), getBlogPost);

// Comment routes
router.post("/:slug/comments", createComment);

// Protected routes (Admin only)
router.post(
  "/",
  authenticate,
  authorize("ADMIN"),
  uploadBlogImage.single("featuredImage"),
  createBlogPost
);
router.put(
  "/:id",
  authenticate,
  authorize("ADMIN"),
  uploadBlogImage.single("featuredImage"),
  updateBlogPost
);
router.delete("/:id", authenticate, authorize("ADMIN"), deleteBlogPost);
router.patch(
  "/:id/toggle-visibility",
  authenticate,
  authorize("ADMIN"),
  toggleBlogPostVisibility
);

export default router;
