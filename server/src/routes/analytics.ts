import express from "express";
import { trackEvent } from "../middleware/analytics";
import { PrismaClient } from "@prisma/client";

const router = express.Router();
const prisma = new PrismaClient();

// @desc    Track custom analytics event
// @route   POST /api/analytics/event
// @access  Public
router.post("/event", async (req, res) => {
  try {
    const {
      eventName,
      eventCategory,
      eventLabel,
      eventValue,
      path,
      language,
      blogPostId,
      metadata,
    } = req.body;

    // Get client info
    const userAgent = req.headers["user-agent"] || "";
    const ipAddress =
      (req.headers["x-forwarded-for"] as string)?.split(",")[0] ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      "unknown";

    // Convert blog post slug to ID if provided
    let resolvedBlogPostId = null;
    if (blogPostId) {
      try {
        const blogPost = await prisma.blogPost.findUnique({
          where: { slug: blogPostId },
          select: { id: true },
        });
        resolvedBlogPostId = blogPost?.id || null;
        console.log(
          `🔍 Analytics: Resolved slug "${blogPostId}" to ID "${resolvedBlogPostId}"`
        );
      } catch (error) {
        console.error("Error resolving blog post ID:", error);
      }
    }

    // Debug logging for timing events
    if (eventName === "timing_complete") {
      console.log(
        `⏱️ Timing event: ${eventValue}s on ${path} for blog post ${resolvedBlogPostId}`
      );
    }

    // Track the event
    await trackEvent(
      eventName,
      eventCategory,
      eventLabel,
      eventValue,
      path,
      language,
      userAgent,
      ipAddress,
      resolvedBlogPostId,
      metadata
    );

    res.json({ success: true });
  } catch (error) {
    console.error("Error tracking event:", error);
    res.status(500).json({
      success: false,
      message: "Failed to track event",
    });
  }
});

export default router;
