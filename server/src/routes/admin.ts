import express from "express";
import rateLimit from "express-rate-limit";
import { authenticate, authorize } from "../middleware/auth";
import {
  uploadBlogImage,
  uploadProductImage,
  uploadProductImages,
} from "../middleware/upload";
import {
  getCategories,
  createCategory,
  updateCategory,
  deleteCategory,
} from "../controllers/categoryController";
import {
  getTags,
  createTag,
  updateTag,
  deleteTag,
} from "../controllers/tagController";
import {
  getBlogAnalytics,
  getBlogPostsAnalytics,
  getConversionAnalytics,
  getStaticPagesAnalytics,
} from "../controllers/analyticsController";
import {
  getAdminProducts,
  createProduct,
  updateProduct,
  deleteProduct,
} from "../controllers/productController";

const router = express.Router();

// More lenient rate limiting for admin routes - disabled in development
if (process.env.NODE_ENV === "production") {
  const adminLimiter = rateLimit({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || "900000"), // 15 minutes
    max: parseInt(process.env.ADMIN_RATE_LIMIT_MAX_REQUESTS || "5000"), // 5000 requests per window for admin
    message: {
      error: "Too many admin requests from this IP, please try again later.",
    },
    standardHeaders: true,
    legacyHeaders: false,
  });

  // Apply admin rate limiting
  router.use(adminLimiter);
}

// All admin routes require authentication
router.use(authenticate);
router.use(authorize("ADMIN"));

// @desc    Get dashboard stats
// @route   GET /api/admin/dashboard
router.get("/dashboard", async (req, res) => {
  try {
    const { PrismaClient } = await import("@prisma/client");
    const prisma = new PrismaClient();

    const [
      totalPosts,
      publishedPosts,
      draftPosts,
      totalCategories,
      totalTags,
      totalComments,
      approvedComments,
    ] = await Promise.all([
      prisma.blogPost.count(),
      prisma.blogPost.count({ where: { published: true } }),
      prisma.blogPost.count({ where: { published: false } }),
      prisma.category.count(),
      prisma.tag.count(),
      prisma.comment.count(),
      prisma.comment.count({ where: { approved: true } }),
    ]);

    res.json({
      success: true,
      data: {
        posts: {
          total: totalPosts,
          published: publishedPosts,
          drafts: draftPosts,
        },
        categories: totalCategories,
        tags: totalTags,
        comments: {
          total: totalComments,
          approved: approvedComments,
          pending: totalComments - approvedComments,
        },
      },
    });
  } catch (error) {
    console.error("Dashboard stats error:", error);
    res.status(500).json({
      success: false,
      message: "Server error",
    });
  }
});

// @desc    Get all blog posts for admin
// @route   GET /api/admin/posts
router.get("/posts", async (req, res) => {
  try {
    const { PrismaClient } = await import("@prisma/client");
    const prisma = new PrismaClient();

    const { page = 1, limit = 10, status, search } = req.query;
    const skip = (Number(page) - 1) * Number(limit);

    const where: any = {};

    if (status === "published") {
      where.published = true;
    } else if (status === "draft") {
      where.published = false;
    }

    if (search) {
      where.translations = {
        some: {
          OR: [
            { title: { contains: search as string, mode: "insensitive" } },
            { content: { contains: search as string, mode: "insensitive" } },
          ],
        },
      };
    }

    const [posts, total] = await Promise.all([
      prisma.blogPost.findMany({
        where,
        include: {
          author: {
            select: { id: true, name: true, email: true },
          },
          translations: true,
          categories: {
            include: { category: true },
          },
          tags: {
            include: { tag: true },
          },
          _count: {
            select: { comments: true },
          },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: Number(limit),
      }),
      prisma.blogPost.count({ where }),
    ]);

    res.json({
      success: true,
      data: {
        posts,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit)),
        },
      },
    });
  } catch (error) {
    console.error("Get admin posts error:", error);
    res.status(500).json({
      success: false,
      message: "Server error",
    });
  }
});

// @desc    Get single blog post for admin (for editing)
// @route   GET /api/admin/posts/:id
router.get("/posts/:id", async (req, res) => {
  try {
    const { PrismaClient } = await import("@prisma/client");
    const prisma = new PrismaClient();

    const { id } = req.params;

    const post = await prisma.blogPost.findUnique({
      where: { id },
      include: {
        author: {
          select: { id: true, name: true, email: true },
        },
        translations: true,
        categories: {
          include: { category: true },
        },
        tags: {
          include: { tag: true },
        },
        _count: {
          select: { comments: true },
        },
      },
    });

    if (!post) {
      return res.status(404).json({
        success: false,
        message: "Blog post not found",
      });
    }

    res.json({
      success: true,
      data: post,
    });
  } catch (error) {
    console.error("Get admin post error:", error);
    res.status(500).json({
      success: false,
      message: "Server error",
    });
  }
});

// @desc    Upload image
// @route   POST /api/admin/upload-image
router.post("/upload-image", uploadBlogImage.single("image"), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: "No image file provided",
      });
    }

    const { getFileUrl } = require("../middleware/upload");
    const imageUrl = getFileUrl(req.file.filename);

    res.json({
      success: true,
      data: {
        filename: req.file.filename,
        url: imageUrl,
        size: req.file.size,
        mimetype: req.file.mimetype,
      },
    });
  } catch (error) {
    console.error("Upload image error:", error);
    res.status(500).json({
      success: false,
      message: "Server error",
    });
  }
});

// Category routes
router.get("/categories", getCategories);
router.post("/categories", createCategory);
router.put("/categories/:id", updateCategory);
router.delete("/categories/:id", deleteCategory);

// Tag routes
router.get("/tags", getTags);
router.post("/tags", createTag);
router.put("/tags/:id", updateTag);
router.delete("/tags/:id", deleteTag);

// Product routes
router.get("/products", getAdminProducts);
router.post(
  "/products",
  uploadProductImages.array("images", 10),
  createProduct
);
router.get("/products/:id", async (req, res) => {
  try {
    const { PrismaClient } = await import("@prisma/client");
    const prisma = new PrismaClient();

    const { id } = req.params;

    const product = await prisma.product.findUnique({
      where: { id },
      include: {
        translations: true,
        categories: {
          include: { category: true },
        },
        tags: {
          include: { tag: true },
        },
        images: {
          orderBy: { sortOrder: "asc" },
        },
      },
    });

    if (!product) {
      return res.status(404).json({
        success: false,
        message: "Product not found",
      });
    }

    res.json({
      success: true,
      product,
    });
  } catch (error) {
    console.error("Get admin product error:", error);
    res.status(500).json({
      success: false,
      message: "Server error",
    });
  }
});
router.put(
  "/products/:id",
  uploadProductImages.array("images", 10),
  updateProduct
);
router.delete("/products/:id", deleteProduct);

// Analytics routes
router.get("/analytics/blog", getBlogAnalytics);
router.get("/analytics/posts", getBlogPostsAnalytics);
router.get("/analytics/conversions", getConversionAnalytics);
router.get("/analytics/pages", getStaticPagesAnalytics);

// Comment management routes
router.get("/comments", async (req, res) => {
  try {
    const { PrismaClient } = await import("@prisma/client");
    const prisma = new PrismaClient();

    const {
      page = 1,
      limit = 10,
      status = "all",
      search = "",
      blogPostId,
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);

    const where: any = {};

    // Filter by approval status
    if (status === "pending") {
      where.approved = false;
    } else if (status === "approved") {
      where.approved = true;
    }

    // Filter by blog post
    if (blogPostId) {
      where.blogPostId = blogPostId as string;
    }

    // Search by author name or email
    if (search) {
      where.OR = [
        { author: { contains: search as string, mode: "insensitive" } },
        { email: { contains: search as string, mode: "insensitive" } },
        { content: { contains: search as string, mode: "insensitive" } },
      ];
    }

    const [comments, total] = await Promise.all([
      prisma.comment.findMany({
        where,
        include: {
          blogPost: {
            select: {
              id: true,
              slug: true,
              translations: {
                select: { title: true, language: true },
                where: { language: "en" },
                take: 1,
              },
            },
          },
          parent: {
            select: { id: true, author: true, content: true },
          },
          replies: {
            select: { id: true, author: true, approved: true },
          },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: Number(limit),
      }),
      prisma.comment.count({ where }),
    ]);

    res.json({
      success: true,
      data: {
        comments,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit)),
        },
      },
    });
  } catch (error) {
    console.error("Get comments error:", error);
    res.status(500).json({
      success: false,
      message: "Server error",
    });
  }
});

// Approve comment
router.patch("/comments/:id/approve", async (req, res) => {
  try {
    const { PrismaClient } = await import("@prisma/client");
    const prisma = new PrismaClient();
    const { id } = req.params;

    const comment = await prisma.comment.update({
      where: { id },
      data: { approved: true },
      include: {
        blogPost: {
          select: {
            slug: true,
            translations: {
              select: { title: true },
              where: { language: "en" },
              take: 1,
            },
          },
        },
      },
    });

    res.json({
      success: true,
      message: "Comment approved successfully",
      data: comment,
    });
  } catch (error) {
    console.error("Approve comment error:", error);
    res.status(500).json({
      success: false,
      message: "Server error",
    });
  }
});

// Reject/Hide comment
router.patch("/comments/:id/reject", async (req, res) => {
  try {
    const { PrismaClient } = await import("@prisma/client");
    const prisma = new PrismaClient();
    const { id } = req.params;

    const comment = await prisma.comment.update({
      where: { id },
      data: { approved: false },
      include: {
        blogPost: {
          select: {
            slug: true,
            translations: {
              select: { title: true },
              where: { language: "en" },
              take: 1,
            },
          },
        },
      },
    });

    res.json({
      success: true,
      message: "Comment rejected/hidden successfully",
      data: comment,
    });
  } catch (error) {
    console.error("Reject comment error:", error);
    res.status(500).json({
      success: false,
      message: "Server error",
    });
  }
});

// Delete comment
router.delete("/comments/:id", async (req, res) => {
  try {
    const { PrismaClient } = await import("@prisma/client");
    const prisma = new PrismaClient();
    const { id } = req.params;

    await prisma.comment.delete({
      where: { id },
    });

    res.json({
      success: true,
      message: "Comment deleted successfully",
    });
  } catch (error) {
    console.error("Delete comment error:", error);
    res.status(500).json({
      success: false,
      message: "Server error",
    });
  }
});

export default router;
