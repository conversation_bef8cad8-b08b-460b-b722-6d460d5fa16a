import Redis from "ioredis";

// Check if Redis should be enabled (disabled in development by default)
const isRedisEnabled =
  process.env.NODE_ENV === "production" || process.env.ENABLE_REDIS === "true";

let redis: Redis | null = null;

if (isRedisEnabled) {
  // Redis configuration
  const redisConfig = {
    host: process.env.REDIS_HOST || "localhost",
    port: parseInt(process.env.REDIS_PORT || "6379"),
    password: process.env.REDIS_PASSWORD || undefined,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
    lazyConnect: true,
  };

  // Parse Redis URL if provided (for Docker/production)
  if (process.env.REDIS_URL) {
    const url = new URL(process.env.REDIS_URL);
    redisConfig.host = url.hostname;
    redisConfig.port = parseInt(url.port) || 6379;
    if (url.password) {
      redisConfig.password = url.password;
    }
  }

  // Create Redis client
  redis = new Redis(redisConfig);

  // Basic event handlers
  redis.on("connect", () => {
    console.log("🔗 Redis connected successfully");
  });

  redis.on("error", (error) => {
    console.error("❌ Redis connection error:", error);
  });
} else {
  console.log("🚀 Redis disabled for development environment");
}

// Simple cache service
export class CacheService {
  static async get<T>(key: string): Promise<T | null> {
    if (!redis) return null;
    try {
      const cached = await redis.get(key);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      console.error("Cache get error:", error);
      return null;
    }
  }

  static async set(
    key: string,
    data: any,
    ttlSeconds: number = 3600
  ): Promise<boolean> {
    if (!redis) return false;
    try {
      await redis.setex(key, ttlSeconds, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error("Cache set error:", error);
      return false;
    }
  }

  static async del(key: string): Promise<boolean> {
    if (!redis) return false;
    try {
      await redis.del(key);
      return true;
    } catch (error) {
      console.error("Cache delete error:", error);
      return false;
    }
  }
}

export default redis;
