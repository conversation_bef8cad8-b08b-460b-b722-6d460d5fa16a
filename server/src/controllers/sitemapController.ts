import { Request, Response } from "express";
import { PrismaClient } from "@prisma/client";
import { getSitemapRoutes, SUPPORTED_LANGUAGES } from "../utils/routeDiscovery";

const prisma = new PrismaClient();
const BASE_URL = process.env.BASE_URL || "https://devskills.ee";

// Generate XML sitemap content
const generateSitemapXML = (urls: any[]) => {
  const urlEntries = urls
    .map(
      (url) => `  <url>
    <loc>${url.loc}</loc>
    <lastmod>${url.lastmod}</lastmod>
    <changefreq>${url.changefreq}</changefreq>
    <priority>${url.priority}</priority>${
        url.alternates
          ? "\n" +
            url.alternates
              .map(
                (alt: any) =>
                  `    <xhtml:link rel="alternate" hreflang="${alt.hreflang}" href="${alt.href}" />`
              )
              .join("\n")
          : ""
      }
  </url>`
    )
    .join("\n");

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml">
${urlEntries}
</urlset>`;
};

// Generate sitemap index XML
const generateSitemapIndexXML = (sitemaps: string[]) => {
  const sitemapEntries = sitemaps
    .map(
      (sitemap) => `
  <sitemap>
    <loc>${BASE_URL}/${sitemap}</loc>
    <lastmod>${new Date().toISOString().split("T")[0]}</lastmod>
  </sitemap>`
    )
    .join("");

  return `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemapEntries}
</sitemapindex>`;
};

// @desc    Generate main sitemap index
// @route   GET /sitemap.xml
// @access  Public
export const getSitemapIndex = async (_req: Request, res: Response) => {
  try {
    const sitemaps = [
      "sitemap-static.xml",
      "sitemap-blog.xml",
      "sitemap-products.xml",
      ...SUPPORTED_LANGUAGES.map((lang) => `sitemap-${lang}.xml`),
    ];

    const sitemapXML = generateSitemapIndexXML(sitemaps);

    res.set({
      "Content-Type": "application/xml",
      "Cache-Control": "public, max-age=3600", // Cache for 1 hour
    });

    res.send(sitemapXML);
  } catch (error) {
    console.error("Error generating sitemap index:", error);
    res.status(500).send("Error generating sitemap");
  }
};

// @desc    Generate static pages sitemap
// @route   GET /sitemap-static.xml
// @access  Public
export const getStaticSitemap = async (_req: Request, res: Response) => {
  try {
    const urls: any[] = [];
    const today = new Date().toISOString().split("T")[0];

    // Add static pages for each language using dynamic route configuration
    const sitemapRoutes = getSitemapRoutes();
    sitemapRoutes.forEach((route) => {
      SUPPORTED_LANGUAGES.forEach((lang) => {
        const url = {
          loc:
            `${BASE_URL}/${lang}/${route.path}`.replace(/\/$/, "") ||
            `${BASE_URL}/${lang}`,
          lastmod: today,
          changefreq: route.changefreq,
          priority: route.priority,
          alternates: SUPPORTED_LANGUAGES.map((altLang) => ({
            hreflang: altLang,
            href:
              `${BASE_URL}/${altLang}/${route.path}`.replace(/\/$/, "") ||
              `${BASE_URL}/${altLang}`,
          })),
        };
        urls.push(url);
      });
    });

    const sitemapXML = generateSitemapXML(urls);

    res.set({
      "Content-Type": "application/xml",
      "Cache-Control": "public, max-age=86400", // Cache for 24 hours
    });

    res.send(sitemapXML);
  } catch (error) {
    console.error("Error generating static sitemap:", error);
    res.status(500).send("Error generating sitemap");
  }
};

// @desc    Generate blog posts sitemap
// @route   GET /sitemap-blog.xml
// @access  Public
export const getBlogSitemap = async (_req: Request, res: Response) => {
  try {
    const urls: any[] = [];

    // Get all published blog posts
    const posts = await prisma.blogPost.findMany({
      where: {
        published: true,
        OR: [{ scheduledAt: null }, { scheduledAt: { lte: new Date() } }],
      },
      include: {
        translations: true,
      },
      orderBy: {
        publishedAt: "desc",
      },
    });

    // Add blog posts for each language they have translations for
    posts.forEach((post) => {
      const availableLanguages = post.translations.map((t) => t.language);

      availableLanguages.forEach((lang) => {
        const translation = post.translations.find((t) => t.language === lang);
        if (translation) {
          const url = {
            loc: `${BASE_URL}/${lang}/blog-single/${post.slug}`,
            lastmod: post.updatedAt.toISOString().split("T")[0],
            changefreq: "weekly",
            priority: "0.8",
            alternates: availableLanguages.map((altLang) => ({
              hreflang: altLang,
              href: `${BASE_URL}/${altLang}/blog-single/${post.slug}`,
            })),
          };
          urls.push(url);
        }
      });
    });

    const sitemapXML = generateSitemapXML(urls);

    res.set({
      "Content-Type": "application/xml",
      "Cache-Control": "public, max-age=3600", // Cache for 1 hour (more dynamic)
    });

    res.send(sitemapXML);
  } catch (error) {
    console.error("Error generating blog sitemap:", error);
    res.status(500).send("Error generating sitemap");
  }
};

// @desc    Generate products sitemap
// @route   GET /sitemap-products.xml
// @access  Public
export const getProductsSitemap = async (_req: Request, res: Response) => {
  try {
    const urls: any[] = [];

    // Get all published products with translations
    const products = await prisma.product.findMany({
      where: {
        status: "published",
      },
      include: {
        translations: true,
      },
      orderBy: {
        publishedAt: "desc",
      },
    });

    // Add products for each language that has translations
    products.forEach((product) => {
      // Get available languages for this product
      const availableLanguages = product.translations.map((t) => t.language);

      // Create URLs for each available language
      availableLanguages.forEach((language) => {
        const url = {
          loc: `${BASE_URL}/${language}/webstore-single/${product.slug}`,
          lastmod: product.updatedAt.toISOString().split("T")[0],
          changefreq: "weekly",
          priority: "0.8",
          alternates: availableLanguages.map((lang) => ({
            hreflang: lang,
            href: `${BASE_URL}/${lang}/webstore-single/${product.slug}`,
          })),
        };
        urls.push(url);
      });
    });

    const sitemapXML = generateSitemapXML(urls);

    res.set({
      "Content-Type": "application/xml",
      "Cache-Control": "public, max-age=3600", // Cache for 1 hour
    });

    res.send(sitemapXML);
  } catch (error) {
    console.error("Error generating products sitemap:", error);
    res.status(500).send("Error generating sitemap");
  }
};

// @desc    Generate language-specific sitemap
// @route   GET /sitemap-:lang.xml
// @access  Public
export const getLanguageSitemap = async (req: Request, res: Response) => {
  try {
    const { lang } = req.params;

    if (!SUPPORTED_LANGUAGES.includes(lang)) {
      return res.status(404).send("Language not supported");
    }

    const urls: any[] = [];
    const today = new Date().toISOString().split("T")[0];

    // Add static pages for this language with hreflang alternates using dynamic route configuration
    const sitemapRoutes = getSitemapRoutes();
    sitemapRoutes.forEach((route) => {
      const url = {
        loc:
          `${BASE_URL}/${lang}/${route.path}`.replace(/\/$/, "") ||
          `${BASE_URL}/${lang}`,
        lastmod: today,
        changefreq: route.changefreq,
        priority: route.priority,
        alternates: SUPPORTED_LANGUAGES.map((altLang) => ({
          hreflang: altLang,
          href:
            `${BASE_URL}/${altLang}/${route.path}`.replace(/\/$/, "") ||
            `${BASE_URL}/${altLang}`,
        })),
      };
      urls.push(url);
    });

    // Add blog posts for this language
    const posts = await prisma.blogPost.findMany({
      where: {
        published: true,
        OR: [{ scheduledAt: null }, { scheduledAt: { lte: new Date() } }],
        translations: {
          some: {
            language: lang,
          },
        },
      },
      include: {
        translations: true, // Get all translations for hreflang alternates
      },
      orderBy: {
        publishedAt: "desc",
      },
    });

    posts.forEach((post) => {
      // Get all available languages for this post
      const availableLanguages = post.translations.map((t) => t.language);

      const url = {
        loc: `${BASE_URL}/${lang}/blog-single/${post.slug}`,
        lastmod: post.updatedAt.toISOString().split("T")[0],
        changefreq: "weekly",
        priority: "0.8",
        alternates: availableLanguages.map((altLang) => ({
          hreflang: altLang,
          href: `${BASE_URL}/${altLang}/blog-single/${post.slug}`,
        })),
      };
      urls.push(url);
    });

    const sitemapXML = generateSitemapXML(urls);

    res.set({
      "Content-Type": "application/xml",
      "Cache-Control": "public, max-age=3600", // Cache for 1 hour
    });

    res.send(sitemapXML);
  } catch (error) {
    console.error("Error generating language sitemap:", error);
    res.status(500).send("Error generating sitemap");
  }
};

// Debug endpoint to see discovered routes
export const getDiscoveredRoutesDebug = async (req: Request, res: Response) => {
  try {
    const { getDiscoveredRoutes } = await import("../utils/routeDiscovery");
    const forceRefresh = req.query.refresh === "true";
    const routes = getDiscoveredRoutes(forceRefresh);

    res.json({
      success: true,
      message: `Discovered ${routes.length} routes from React Router`,
      routes: routes,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Error getting discovered routes:", error);
    res.status(500).json({
      success: false,
      message: "Failed to discover routes",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
