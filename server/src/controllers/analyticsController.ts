import { Request, Response } from "express";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

// Helper function to get date range based on timeRange parameter
const getDateRange = (timeRange: string) => {
  const now = new Date();
  const ranges: { [key: string]: { start: Date; end: Date } } = {
    lastday: {
      start: new Date(now.getTime() - 24 * 60 * 60 * 1000),
      end: now,
    },
    lastweek: {
      start: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000),
      end: now,
    },
    last14days: {
      start: new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000),
      end: now,
    },
    last30days: {
      start: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000),
      end: now,
    },
    last2months: {
      start: new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000),
      end: now,
    },
    last4months: {
      start: new Date(now.getTime() - 120 * 24 * 60 * 60 * 1000),
      end: now,
    },
    last6months: {
      start: new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000),
      end: now,
    },
  };

  return ranges[timeRange] || ranges.last30days;
};

// Helper function to process analytics data
const processAnalyticsData = async (
  pageViews: any[],
  events: any[],
  blogPostViews: any[],
  start: Date,
  end: Date,
  _timeRange: string,
  language: string = "all"
) => {
  const days = Math.ceil(
    (end.getTime() - start.getTime()) / (24 * 60 * 60 * 1000)
  );

  // Generate labels based on time range
  const labels = [];
  const pageViewsData = [];
  const visitorsData = [];
  const engagementData = [];

  // Group data by day
  const dailyData = new Map();

  for (let i = 0; i < days; i++) {
    const date = new Date(start.getTime() + i * 24 * 60 * 60 * 1000);
    const dateKey = date.toISOString().split("T")[0];
    labels.push(
      date.toLocaleDateString("en-US", { month: "short", day: "numeric" })
    );
    dailyData.set(dateKey, {
      pageViews: 0,
      visitors: new Set(),
      engagement: 0,
    });
  }

  // Process page views
  pageViews.forEach((view) => {
    const dateKey = view.timestamp.toISOString().split("T")[0];
    if (dailyData.has(dateKey)) {
      const dayData = dailyData.get(dateKey);
      dayData.pageViews++;
      dayData.visitors.add(view.sessionId);
    }
  });

  // Process engagement events
  events.forEach((event) => {
    const dateKey = event.timestamp.toISOString().split("T")[0];
    if (dailyData.has(dateKey)) {
      const dayData = dailyData.get(dateKey);
      dayData.engagement++;
    }
  });

  // Convert to arrays
  for (const [_dateKey, data] of dailyData) {
    pageViewsData.push(data.pageViews);
    visitorsData.push(data.visitors.size);
    engagementData.push(data.engagement);
  }

  // Add blog post view counts to the data (distribute across the time period)
  const totalBlogPostViews = blogPostViews.reduce(
    (sum, post) => sum + (post.viewCount || 0),
    0
  );

  const totalPageViewRecords = pageViewsData.reduce((a, b) => a + b, 0);

  // If we have blog post views, add them to the totals
  if (totalBlogPostViews > 0) {
    console.log(
      "📈 Blog post view counts:",
      totalBlogPostViews,
      "Page view records:",
      totalPageViewRecords
    );

    // If we have more blog post views than page view records, add the difference
    const additionalViews = Math.max(
      0,
      totalBlogPostViews - totalPageViewRecords
    );
    if (additionalViews > 0) {
      // Distribute the additional views across the time period (simple approach: put them on the last day)
      const lastDayIndex = pageViewsData.length - 1;
      if (lastDayIndex >= 0) {
        pageViewsData[lastDayIndex] += additionalViews;
        // Estimate visitors as roughly 70% of additional views (typical conversion)
        visitorsData[lastDayIndex] += Math.floor(additionalViews * 0.7);
      }
    }
  }

  // Generate heatmap data (7 days x 24 hours)
  const heatmapData = await generateHeatmapData(pageViews);

  // Calculate previous period for comparison
  const previousStart = new Date(
    start.getTime() - (end.getTime() - start.getTime())
  );
  const previousEnd = start;

  const previousPageViews = await prisma.pageView.count({
    where: {
      timestamp: { gte: previousStart, lte: previousEnd },
      ...(language !== "all" && {
        path: { startsWith: `/${language}/` },
      }),
    },
  });

  const previousVisitors = await prisma.pageView.findMany({
    where: {
      timestamp: { gte: previousStart, lte: previousEnd },
    },
    select: { sessionId: true },
    distinct: ["sessionId"],
  });

  const previousEngagement = await prisma.analyticsEvent.count({
    where: {
      timestamp: { gte: previousStart, lte: previousEnd },
    },
  });

  const currentPageViews = pageViewsData.reduce((a, b) => a + b, 0);
  const currentVisitors = visitorsData.reduce((a, b) => a + b, 0);

  // If we have blog post views but no visitors, estimate visitors
  const estimatedVisitors =
    totalBlogPostViews > 0 && currentVisitors === 0
      ? Math.floor(totalBlogPostViews * 0.7)
      : currentVisitors;

  return {
    overview: {
      pageViews: {
        current: Math.max(currentPageViews, totalBlogPostViews),
        previous: previousPageViews,
      },
      visitors: {
        current: estimatedVisitors,
        previous: previousVisitors.length,
      },
      engagement: {
        current: engagementData.reduce((a, b) => a + b, 0),
        previous: previousEngagement,
      },
    },
    chartData: {
      labels,
      pageViews: pageViewsData,
      visitors: visitorsData,
      engagement: engagementData,
    },
    heatmapData: {
      heatmapData,
    },
    comparedPeriod: `${start.toLocaleDateString()} - ${end.toLocaleDateString()}`,
  };
};

// Helper function to generate heatmap data
const generateHeatmapData = async (pageViews: any[]) => {
  const heatmapData = Array.from({ length: 7 }, () => Array(24).fill(0));

  pageViews.forEach((view) => {
    const date = new Date(view.timestamp);
    let dayOfWeek = date.getDay(); // 0 = Sunday, 6 = Saturday
    // Convert to Monday = 0, Sunday = 6
    dayOfWeek = dayOfWeek === 0 ? 6 : dayOfWeek - 1;
    const hour = date.getHours();
    heatmapData[dayOfWeek][hour]++;
  });

  return heatmapData;
};

// @desc    Get blog analytics data
// @route   GET /api/admin/analytics/blog
// @access  Private (Admin only)
export const getBlogAnalytics = async (req: Request, res: Response) => {
  try {
    const { timeRange = "last30days", language = "all" } = req.query;
    const { start, end } = getDateRange(timeRange as string);

    // Build where clause for language filtering
    const whereClause: any = {
      timestamp: {
        gte: start,
        lte: end,
      },
    };

    // For page views, filter by path pattern instead of language field
    const pageViewWhereClause: any = {
      timestamp: {
        gte: start,
        lte: end,
      },
    };

    if (language !== "all") {
      // Filter by URL path pattern for specific language
      pageViewWhereClause.path = {
        startsWith: `/${language}/`,
      };
      whereClause.language = language as string;
    }

    // Get page views data
    const pageViews = await prisma.pageView.findMany({
      where: pageViewWhereClause,
      select: {
        timestamp: true,
        path: true,
        sessionId: true,
        blogPostId: true,
      },
    });

    // Also get blog post view counts as fallback
    const blogPostViews = await prisma.blogPost.findMany({
      where: {
        published: true,
        publishedAt: {
          gte: start,
          lte: end,
        },
      },
      select: {
        id: true,
        viewCount: true,
        publishedAt: true,
      },
    });

    // Get analytics events data
    const events = await prisma.analyticsEvent.findMany({
      where: whereClause,
      select: {
        timestamp: true,
        eventName: true,
        sessionId: true,
      },
    });

    // Debug logging
    console.log("🔍 Analytics Debug:");
    console.log("📅 Date range:", { start, end, timeRange, language });
    console.log("🔍 Page view where clause:", pageViewWhereClause);
    console.log("🔍 Events where clause:", whereClause);

    // Check total counts without filters
    const totalPageViews = await prisma.pageView.count();
    const totalEvents = await prisma.analyticsEvent.count();
    console.log("📊 Total page views in DB:", totalPageViews);
    console.log("🎯 Total events in DB:", totalEvents);

    console.log("📊 Filtered page views found:", pageViews.length);
    console.log("🎯 Filtered events found:", events.length);
    console.log("📝 Sample page views:", pageViews.slice(0, 3));
    console.log("🎪 Sample events:", events.slice(0, 3));

    // Process data for charts
    const analyticsData = await processAnalyticsData(
      pageViews,
      events,
      blogPostViews,
      start,
      end,
      timeRange as string,
      language as string
    );

    res.json({
      success: true,
      data: analyticsData,
    });
  } catch (error) {
    console.error("Error fetching blog analytics:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch blog analytics",
      error:
        process.env.NODE_ENV === "development"
          ? error.message
          : "Internal server error",
    });
  }
};

// @desc    Get blog posts analytics data
// @route   GET /api/admin/analytics/posts
// @access  Private (Admin only)
export const getBlogPostsAnalytics = async (req: Request, res: Response) => {
  try {
    const { timeRange = "last30days", language = "all" } = req.query;
    const { start, end } = getDateRange(timeRange as string);

    // Build where clause for language filtering
    const whereClause: any = {
      published: true,
      publishedAt: {
        gte: start,
        lte: end,
      },
    };

    // Add language filter if specified
    if (language !== "all") {
      whereClause.translations = {
        some: {
          language: language as string,
        },
      };
    }

    // Fetch blog posts with analytics data
    const posts = await prisma.blogPost.findMany({
      where: whereClause,
      include: {
        translations: {
          where: language !== "all" ? { language: language as string } : {},
        },
        categories: {
          include: {
            category: true,
          },
        },
        tags: true,
      },
      orderBy: {
        publishedAt: "desc",
      },
    });

    // Get analytics data for each post
    const postsWithAnalytics = await Promise.all(
      posts.map(async (post) => {
        // Get actual view count from page views (server-side tracking)
        const views = await prisma.pageView.count({
          where: {
            blogPostId: post.id,
            timestamp: { gte: start, lte: end },
            ...(language !== "all" && { language: language as string }),
          },
        });

        // Get click events (assuming you track click events)
        const clicks = await prisma.analyticsEvent.count({
          where: {
            blogPostId: post.id,
            eventName: "click",
            timestamp: { gte: start, lte: end },
            ...(language !== "all" && { language: language as string }),
          },
        });

        // Get engagement events (likes, shares, comments, etc.)
        const engagement = await prisma.analyticsEvent.count({
          where: {
            blogPostId: post.id,
            eventName: { in: ["like", "share", "comment", "scroll_depth"] },
            timestamp: { gte: start, lte: end },
            ...(language !== "all" && { language: language as string }),
          },
        });

        // Calculate average view time from timing_complete events
        const timeEvents = await prisma.analyticsEvent.findMany({
          where: {
            blogPostId: post.id,
            eventName: "timing_complete",
            timestamp: { gte: start, lte: end },
            ...(language !== "all" && { language: language as string }),
          },
          select: {
            eventValue: true,
          },
        });

        const avgViewTime =
          timeEvents.length > 0
            ? Math.round(
                timeEvents.reduce(
                  (sum, event) => sum + (event.eventValue || 0),
                  0
                ) / timeEvents.length
              )
            : 0;

        return {
          id: post.id,
          title: (post as any).translations[0]?.title || "Untitled",
          publishedAt: post.publishedAt,
          featuredImage: post.featuredImage,
          readingTime: post.readTime || 0,
          avgViewTime, // Real calculated average view time in seconds
          categories: (post as any).categories.map((cat: any) => ({
            id: cat.category.id,
            name: cat.category.name || cat.category.slug,
          })),
          views: views || post.viewCount || 0, // Fallback to stored view count
          clicks,
          engagement,
        };
      })
    );

    res.json({
      success: true,
      data: postsWithAnalytics,
    });
  } catch (error) {
    console.error("Error fetching blog posts analytics:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch blog posts analytics",
      error:
        process.env.NODE_ENV === "development"
          ? error.message
          : "Internal server error",
    });
  }
};

// Get conversion analytics (Business Comanager CTAs)
export const getConversionAnalytics = async (req: Request, res: Response) => {
  try {
    const { startDate, endDate, language = "all" } = req.query;

    // Default to last 30 days if no dates provided
    const start = startDate
      ? new Date(startDate as string)
      : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const end = endDate ? new Date(endDate as string) : new Date();

    // Ensure end date includes the full day
    end.setHours(23, 59, 59, 999);

    console.log("🔍 Conversion Analytics Debug:");
    console.log("📅 Date range:", { start, end, startDate, endDate, language });

    // Get all comanager conversion events
    const conversionEvents = await prisma.analyticsEvent.findMany({
      where: {
        eventName: "comanager_conversion",
        timestamp: { gte: start, lte: end },
        ...(language !== "all" && { language: language as string }),
      },
      select: {
        id: true,
        eventLabel: true,
        eventValue: true,
        path: true,
        language: true,
        timestamp: true,
        metadata: true,
      },
      orderBy: { timestamp: "desc" },
    });

    console.log("🎯 Found conversion events:", conversionEvents.length);
    if (conversionEvents.length > 0) {
      console.log("📝 Sample conversion event:", conversionEvents[0]);
    }

    // Group by source location
    const conversionsBySource = conversionEvents.reduce((acc, event) => {
      const metadata = event.metadata as any;
      const sourceLocation = metadata?.source_location || "unknown";
      const ctaType = metadata?.cta_type || "unknown";

      if (!acc[sourceLocation]) {
        acc[sourceLocation] = {
          source: sourceLocation,
          total_conversions: 0,
          total_value: 0,
          cta_types: {},
          languages: {},
          recent_conversions: [],
        };
      }

      acc[sourceLocation].total_conversions += 1;
      acc[sourceLocation].total_value += event.eventValue || 100;

      // Track CTA types
      if (!acc[sourceLocation].cta_types[ctaType]) {
        acc[sourceLocation].cta_types[ctaType] = 0;
      }
      acc[sourceLocation].cta_types[ctaType] += 1;

      // Track languages
      if (!acc[sourceLocation].languages[event.language]) {
        acc[sourceLocation].languages[event.language] = 0;
      }
      acc[sourceLocation].languages[event.language] += 1;

      // Add to recent conversions (limit to 10)
      if (acc[sourceLocation].recent_conversions.length < 10) {
        acc[sourceLocation].recent_conversions.push({
          timestamp: event.timestamp,
          language: event.language,
          path: event.path,
          cta_type: ctaType,
        });
      }

      return acc;
    }, {} as any);

    // Calculate summary statistics
    const totalConversions = conversionEvents.length;
    const totalValue = conversionEvents.reduce(
      (sum, event) => sum + (event.eventValue || 100),
      0
    );
    const averageValue =
      totalConversions > 0 ? totalValue / totalConversions : 0;

    // Language breakdown
    const languageBreakdown = conversionEvents.reduce((acc, event) => {
      if (!acc[event.language]) {
        acc[event.language] = 0;
      }
      acc[event.language] += 1;
      return acc;
    }, {} as any);

    res.json({
      success: true,
      data: {
        summary: {
          total_conversions: totalConversions,
          total_value: totalValue,
          average_value: Math.round(averageValue),
          date_range: {
            start: start.toISOString(),
            end: end.toISOString(),
          },
        },
        conversions_by_source: Object.values(conversionsBySource),
        language_breakdown: languageBreakdown,
        recent_events: conversionEvents.slice(0, 20).map((event) => ({
          id: event.id,
          timestamp: event.timestamp,
          language: event.language,
          path: event.path,
          source: (event.metadata as any)?.source_location || "unknown",
          cta_type: (event.metadata as any)?.cta_type || "unknown",
          value: event.eventValue || 100,
        })),
      },
    });
  } catch (error) {
    console.error("Error fetching conversion analytics:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch conversion analytics",
    });
  }
};

// Get static pages analytics
export const getStaticPagesAnalytics = async (req: Request, res: Response) => {
  try {
    const { timeRange = "last30days", language = "all" } = req.query;
    const { start, end } = getDateRange(timeRange as string);

    // Get page views from both page_views table and analytics_events table
    const pageViewsFromTable = await prisma.pageView.findMany({
      where: {
        timestamp: { gte: start, lte: end },
        blogPostId: null, // Only static pages
        ...(language !== "all" && { language: language as string }),
        // Exclude system endpoints only
        NOT: {
          OR: [
            { path: { startsWith: "/api/" } },
            { path: { startsWith: "/admin/" } },
            { path: { startsWith: "/uploads/" } },
            { path: { startsWith: "/cosmos/" } },
            { path: { startsWith: "/eth/" } },
            { path: { contains: "node_info" } },
            { path: { contains: "spec" } },
            { path: { equals: "/health" } },
            { path: { equals: "/status" } },
          ],
        },
      },
      select: {
        path: true,
        language: true,
        timestamp: true,
        sessionId: true,
        userAgent: true,
        ipAddress: true,
      },
    });

    // Get page views from analytics_events table (for React SPA)
    const pageViewEvents = await prisma.analyticsEvent.findMany({
      where: {
        eventName: "page_view",
        timestamp: { gte: start, lte: end },
        blogPostId: null,
        ...(language !== "all" && { language: language as string }),
        // Exclude system endpoints only
        NOT: {
          OR: [
            { path: { startsWith: "/api/" } },
            { path: { startsWith: "/admin/" } },
            { path: { startsWith: "/uploads/" } },
            { path: { startsWith: "/cosmos/" } },
            { path: { startsWith: "/eth/" } },
            { path: { contains: "node_info" } },
            { path: { contains: "spec" } },
            { path: { equals: "/health" } },
            { path: { equals: "/status" } },
          ],
        },
      },
      select: {
        path: true,
        language: true,
        timestamp: true,
        sessionId: true,
        userAgent: true,
        ipAddress: true,
      },
    });

    // Combine both sources
    const pageViews = [
      ...pageViewsFromTable,
      ...pageViewEvents.map((event) => ({
        path: event.path,
        language: event.language,
        timestamp: event.timestamp,
        sessionId: event.sessionId,
        userAgent: event.userAgent,
        ipAddress: event.ipAddress,
      })),
    ];

    // Debug: Let's see what pages exist in the database
    const allPageViews = await prisma.pageView.findMany({
      where: {
        timestamp: { gte: start, lte: end },
        blogPostId: null,
      },
      select: {
        path: true,
        language: true,
      },
      take: 10,
    });

    // Also check analytics_events for page_view events
    const debugPageViewEvents = await prisma.analyticsEvent.findMany({
      where: {
        eventName: "page_view",
        timestamp: { gte: start, lte: end },
        blogPostId: null,
      },
      select: {
        path: true,
        language: true,
      },
      take: 10,
    });

    console.log(
      "🔍 Debug: All static page views in page_views table:",
      allPageViews.map((p) => ({ path: p.path, language: p.language }))
    );
    console.log(
      "🔍 Debug: All page_view events in analytics_events table:",
      debugPageViewEvents.map((p) => ({ path: p.path, language: p.language }))
    );
    console.log(
      "📄 Found filtered page views for static pages:",
      pageViews.length
    );
    if (pageViews.length > 0) {
      console.log(
        "📝 Sample filtered page views:",
        pageViews
          .slice(0, 3)
          .map((p) => ({ path: p.path, language: p.language }))
      );
    }

    // Get timing events for average time on page
    const timingEvents = await prisma.analyticsEvent.findMany({
      where: {
        eventName: "timing_complete",
        timestamp: { gte: start, lte: end },
        blogPostId: null,
        ...(language !== "all" && { language: language as string }),
        // Filter out system/API endpoints
        path: {
          not: {
            in: ["/health", "/status", "/api", "/admin"],
          },
        },
        AND: [
          { path: { not: { startsWith: "/api/" } } },
          { path: { not: { startsWith: "/admin/" } } },
          { path: { not: { startsWith: "/uploads/" } } },
          { path: { not: { startsWith: "/cosmos/" } } },
          { path: { not: { startsWith: "/eth/" } } },
          { path: { not: { contains: "node_info" } } },
          { path: { not: { contains: "spec" } } },
        ],
      },
      select: {
        path: true,
        eventValue: true,
        language: true,
      },
    });

    // Group page views by path
    const pageStats = pageViews.reduce((acc, view) => {
      const path = view.path;

      if (!acc[path]) {
        acc[path] = {
          path,
          views: 0,
          unique_visitors: new Set(),
          languages: {},
          sessions: new Set(),
          page_type: getPageType(path),
          page_title: getPageTitle(path),
        };
      }

      acc[path].views += 1;
      acc[path].unique_visitors.add(view.ipAddress);
      acc[path].sessions.add(view.sessionId);

      // Track languages
      if (!acc[path].languages[view.language]) {
        acc[path].languages[view.language] = 0;
      }
      acc[path].languages[view.language] += 1;

      return acc;
    }, {} as any);

    // Add timing data
    timingEvents.forEach((event) => {
      const path = event.path;
      if (pageStats[path]) {
        if (!pageStats[path].timings) {
          pageStats[path].timings = [];
        }
        pageStats[path].timings.push(event.eventValue || 0);
      }
    });

    // Calculate final metrics
    const pagesData = Object.values(pageStats).map((page: any) => {
      const uniqueVisitors = page.unique_visitors.size;
      const sessions = page.sessions.size;

      // Calculate average time on page
      const avgTimeOnPage =
        page.timings && page.timings.length > 0
          ? page.timings.reduce((sum: number, time: number) => sum + time, 0) /
            page.timings.length
          : 0;

      // Calculate bounce rate (sessions with only 1 page view)
      const bounceRate =
        sessions > 0 ? ((sessions - uniqueVisitors) / sessions) * 100 : 0;

      return {
        path: page.path,
        page_title: page.page_title,
        page_type: page.page_type,
        views: page.views,
        unique_visitors: uniqueVisitors,
        avg_time_on_page: Math.round(avgTimeOnPage),
        bounce_rate: Math.round(bounceRate),
        languages: page.languages,
      };
    });

    // Sort by views descending
    pagesData.sort((a, b) => b.views - a.views);

    res.json({
      success: true,
      data: pagesData,
    });
  } catch (error) {
    console.error("Error fetching static pages analytics:", error);
    res.status(500).json({
      success: false,
      error: "Failed to fetch static pages analytics",
    });
  }
};

// Helper function to get page type from path
function getPageType(path: string): string {
  if (path === "/" || path.match(/^\/[a-z]{2}$/)) return "home";
  if (path.includes("/about")) return "about";
  if (path.includes("/services")) return "services";
  if (path.includes("/products")) return "products";
  if (path.includes("/contact")) return "contact";
  if (path.includes("/blog")) return "blog";
  return "other";
}

// Helper function to get page title from path
function getPageTitle(path: string): string {
  const pathMap: { [key: string]: string } = {
    "/": "Home",
    "/en": "Home (EN)",
    "/et": "Home (ET)",
    "/de": "Home (DE)",
    "/fi": "Home (FI)",
    "/sv": "Home (SV)",
    "/about": "About",
    "/services": "Services",
    "/contact": "Contact",
    "/products": "Products",
    "/products/bms": "Business Comanager",
    "/blog": "Blog",
  };

  // Check for exact matches first
  if (pathMap[path]) {
    return pathMap[path];
  }

  // Handle language-prefixed paths
  const segments = path.split("/").filter(Boolean);
  if (segments.length >= 2) {
    const langCode = segments[0];
    const pagePath = "/" + segments.slice(1).join("/");

    if (pathMap[pagePath]) {
      return `${pathMap[pagePath]} (${langCode.toUpperCase()})`;
    }
  }

  // Fallback: convert path to title
  return (
    path
      .split("/")
      .filter(Boolean)
      .join(" / ")
      .replace(/-/g, " ")
      .replace(/\b\w/g, (l) => l.toUpperCase()) || "Home"
  );
}
