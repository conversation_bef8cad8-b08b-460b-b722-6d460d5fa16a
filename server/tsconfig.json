{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "declarationMap": false, "sourceMap": false, "removeComments": true, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "noImplicitThis": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "moduleResolution": "node", "baseUrl": "./src", "paths": {"@/*": ["*"], "@/controllers/*": ["controllers/*"], "@/routes/*": ["routes/*"], "@/middleware/*": ["middleware/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"], "@/prisma/*": ["prisma/*"]}, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "blog-automation", "blog-posts"]}