{"name": "devskills-backend", "version": "1.0.0", "description": "DevSkills backend API with contact forms, blog management and authentication", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon src/index.ts", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx src/prisma/seed.ts", "install:deps": "pnpm install", "blog:start": "node blog-automation/blog-publisher.js", "blog:publish": "node blog-automation/blog-publisher.js publish", "blog:status": "node blog-automation/blog-publisher.js status", "blog:check": "node blog-automation/blog-publisher.js check"}, "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0", "dependencies": {"@prisma/client": "^6.10.1", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dompurify": "^3.0.7", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "gray-matter": "^4.0.3", "helmet": "^7.1.0", "ioredis": "^5.6.1", "joi": "^17.11.0", "jsdom": "^23.0.1", "jsonwebtoken": "^9.0.2", "marked": "^11.1.1", "morgan": "^1.10.0", "multer": "^2.0.1", "nodemailer": "^6.9.8", "slugify": "^1.6.6"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/dompurify": "^3.0.5", "@types/express": "^4.17.21", "@types/jsdom": "^21.1.6", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/nodemailer": "^6.4.14", "nodemon": "^3.0.2", "prisma": "^6.10.1", "tsx": "^4.6.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}, "keywords": ["contact-form", "nodemailer", "express", "api", "blog", "automation", "seo"], "author": "DevSkills", "license": "MIT"}