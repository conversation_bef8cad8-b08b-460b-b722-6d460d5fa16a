# DevSkills Backend Setup Guide

## 1. Install Dependencies

```bash
cd server
npm install
```

## 2. Database Setup

```bash
# Generate Prisma client
npm run db:generate

# Push schema to database (creates tables)
npm run db:push

# Seed the database with initial data
npm run db:seed
```

## 3. Environment Variables

Make sure your `.env` file has the correct database URL:

```
DATABASE_URL="postgresql://root:Onamission%23007@************:5432/devskills"
```

## 4. Start Development Server

```bash
npm run dev
```

## 5. Test Authentication

### Login with the seeded admin user:

```bash
curl -X POST http://localhost:4004/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "Valgehunt#1405"
  }'
```

### Test protected route:

```bash
curl -X GET http://localhost:4004/api/auth/me \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

## 6. Database Management

- View database: `npm run db:studio`
- Create migration: `npm run db:migrate`
- Reset database: `npx prisma migrate reset`

## 7. Build for Production

```bash
npm run build
npm start
```

## API Endpoints

### Authentication

- `POST /api/auth/login` - Login user
- `GET /api/auth/me` - Get current user
- `POST /api/auth/logout` - Logout user
- `POST /api/auth/register` - Register new user (admin only)

### Blog (Coming Soon)

- `GET /api/blog` - Get all blog posts
- `POST /api/blog` - Create blog post
- `GET /api/blog/:slug` - Get single blog post
- `PUT /api/blog/:id` - Update blog post
- `DELETE /api/blog/:id` - Delete blog post

### Admin (Coming Soon)

- `GET /api/admin/dashboard` - Dashboard data
- `GET /api/admin/posts` - Manage blog posts
- `GET /api/admin/categories` - Manage categories
- `GET /api/admin/tags` - Manage tags
- `GET /api/admin/comments` - Manage comments
