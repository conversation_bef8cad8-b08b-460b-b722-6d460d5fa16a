#!/bin/sh

# Docker entrypoint script to handle permissions and start the application

echo "🚀 Starting DevSkills Backend..."

# Function to fix upload directory permissions
fix_upload_permissions() {
    echo "📁 Setting up upload directories..."

    # Check if uploads directory is mounted
    if mountpoint -q /app/uploads; then
        echo "📦 /app/uploads is a mount point (Docker volume)"
    else
        echo "📁 /app/uploads is a regular directory"
    fi

    # Create directories if they don't exist
    mkdir -p /app/uploads/blog-images
    mkdir -p /app/uploads/temp

    # Fix ownership and permissions (we're running as root initially)
    echo "🔧 Fixing ownership and permissions..."
    chown -R nodejs:nodejs /app/uploads
    chmod -R 755 /app/uploads

    # List directory contents for debugging
    echo "📋 Upload directory contents:"
    ls -la /app/uploads/
    ls -la /app/uploads/blog-images/ 2>/dev/null || echo "blog-images directory empty"

    echo "✅ Upload directories ready"
}

# Fix upload permissions (running as root)
fix_upload_permissions

# Switch to nodejs user and start the application
echo "🎯 Switching to nodejs user and starting application..."
exec su-exec nodejs "$@"
