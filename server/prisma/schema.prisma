generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String     @id @default(cuid())
  email     String     @unique
  password  String
  name      String?
  role      Role       @default(ADMIN)
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
  blogPosts BlogPost[]
  comments  Comment[]

  @@map("users")
}

model BlogPost {
  id               String                @id @default(cuid())
  slug             String                @unique
  published        Boolean               @default(false)
  featured         Boolean               @default(false)
  publishedAt      DateTime?
  scheduledAt      DateTime?
  createdAt        DateTime              @default(now())
  updatedAt        DateTime              @updatedAt
  authorId         String
  featuredImage    String?
  featuredImageAlt String?
  gallery          String[]
  metaImage        String?
  readTime         Int?
  viewCount        Int                   @default(0)
  categories       BlogPostCategory[]
  tags             BlogPostTag[]
  translations     BlogPostTranslation[]
  author           User                  @relation(fields: [authorId], references: [id], onDelete: Cascade)
  comments         Comment[]
  pageViews        PageView[]
  analyticsEvents  AnalyticsEvent[]

  @@map("blog_posts")
}

model BlogPostTranslation {
  id          String   @id @default(cuid())
  language    String
  title       String
  excerpt     String?
  content     String
  metaTitle   String?
  metaDesc    String?
  keywords    String[]
  blogPostId  String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  contentType String   @default("html")
  blogPost    BlogPost @relation(fields: [blogPostId], references: [id], onDelete: Cascade)

  @@unique([blogPostId, language])
  @@map("blog_post_translations")
}

model Category {
  id          String             @id @default(cuid())
  slug        String             @unique
  name        String
  description String?
  color       String?
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
  blogPosts   BlogPostCategory[]
  products    ProductCategory[]

  @@map("categories")
}

model Tag {
  id        String        @id @default(cuid())
  slug      String        @unique
  name      String
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt
  blogPosts BlogPostTag[]
  products  ProductTag[]

  @@map("tags")
}

model BlogPostCategory {
  blogPostId String
  categoryId String
  blogPost   BlogPost @relation(fields: [blogPostId], references: [id], onDelete: Cascade)
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  @@id([blogPostId, categoryId])
  @@map("blog_post_categories")
}

model BlogPostTag {
  blogPostId String
  tagId      String
  blogPost   BlogPost @relation(fields: [blogPostId], references: [id], onDelete: Cascade)
  tag        Tag      @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([blogPostId, tagId])
  @@map("blog_post_tags")
}

model Comment {
  id         String    @id @default(cuid())
  content    String
  author     String
  email      String
  website    String?
  approved   Boolean   @default(false)
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  blogPostId String
  userId     String?
  parentId   String?
  blogPost   BlogPost  @relation(fields: [blogPostId], references: [id], onDelete: Cascade)
  parent     Comment?  @relation("CommentReplies", fields: [parentId], references: [id], onDelete: Cascade)
  replies    Comment[] @relation("CommentReplies")
  user       User?     @relation(fields: [userId], references: [id])

  @@map("comments")
}

enum Role {
  ADMIN
  EDITOR
  USER
}

model PageView {
  id          String   @id @default(cuid())
  path        String
  language    String
  userAgent   String?
  ipAddress   String?
  referrer    String?
  sessionId   String?
  timestamp   DateTime @default(now())
  blogPostId  String?
  blogPost    BlogPost? @relation(fields: [blogPostId], references: [id], onDelete: Cascade)

  @@map("page_views")
  @@index([path, language])
  @@index([timestamp])
  @@index([blogPostId])
}

model AnalyticsEvent {
  id          String   @id @default(cuid())
  eventName   String
  eventCategory String?
  eventLabel  String?
  eventValue  Int?
  path        String
  language    String
  userAgent   String?
  ipAddress   String?
  sessionId   String?
  timestamp   DateTime @default(now())
  blogPostId  String?
  blogPost    BlogPost? @relation(fields: [blogPostId], references: [id], onDelete: Cascade)
  metadata    Json?

  @@map("analytics_events")
  @@index([eventName, timestamp])
  @@index([path, language])
  @@index([blogPostId])
}

// Webstore Models
model Product {
  id                String               @id @default(cuid())
  slug              String               @unique
  featuredImage     String?
  featuredImageAlt  String?
  whitelabelPrice   Decimal?             @db.Decimal(10, 2)
  subscriptionPrice Decimal?             @db.Decimal(10, 2)
  demoUrl           String?
  status            String               @default("draft") // draft, published
  readingTime       Int?
  viewCount         Int                  @default(0)
  createdAt         DateTime             @default(now())
  updatedAt         DateTime             @updatedAt
  publishedAt       DateTime?
  categories        ProductCategory[]
  tags              ProductTag[]
  translations      ProductTranslation[]
  images            ProductImage[]

  @@map("products")
}

model ProductImage {
  id          String   @id @default(cuid())
  filename    String
  alt         String?
  sortOrder   Int      @default(0)
  isDisplay   Boolean  @default(false) // The main display image for cards
  productId   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  product     Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

model ProductTranslation {
  id          String   @id @default(cuid())
  language    String
  title       String
  excerpt     String?
  content     String
  metaTitle   String?
  metaDesc    String?
  keywords    String[]
  productId   String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  product     Product  @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@unique([productId, language])
  @@map("product_translations")
}

model ProductCategory {
  productId  String
  categoryId String
  product    Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  category   Category @relation(fields: [categoryId], references: [id], onDelete: Cascade)

  @@id([productId, categoryId])
  @@map("product_categories")
}

model ProductTag {
  productId String
  tagId     String
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  tag       Tag     @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([productId, tagId])
  @@map("product_tags")
}
