#!/bin/bash

# DevSkills Backend Setup Script
echo "🚀 Setting up DevSkills Backend..."

# Check if pnpm is installed
if ! command -v pnpm &> /dev/null; then
    echo "❌ pnpm is not installed. Please install pnpm first:"
    echo "npm install -g pnpm"
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
pnpm install

# Check if .env file exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please create it with the required environment variables."
    echo "See .env.example for reference."
    exit 1
fi

# Generate Prisma client
echo "🔧 Generating Prisma client..."
pnpm db:generate

# Push database schema
echo "🗄️ Setting up database schema..."
pnpm db:push

# Seed the database
echo "🌱 Seeding database with initial data..."
pnpm db:seed

# Create uploads directory
echo "📁 Creating uploads directory..."
mkdir -p uploads/blog-images
mkdir -p uploads/temp

echo "✅ Backend setup complete!"
echo ""
echo "🎯 Next steps:"
echo "1. Start the development server: pnpm dev"
echo "2. Access admin panel at: http://localhost:3000/admin"
echo "3. Login with: <EMAIL> / Valgehunt#1405"
echo ""
echo "📚 Available commands:"
echo "- pnpm dev          # Start development server"
echo "- pnpm build        # Build for production"
echo "- pnpm start        # Start production server"
echo "- pnpm db:studio    # Open Prisma Studio"
echo "- pnpm db:migrate   # Create new migration"
echo ""
